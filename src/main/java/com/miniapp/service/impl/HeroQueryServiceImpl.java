package com.miniapp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.miniapp.service.*;
import com.model.*;
import com.model.dto.HeroDetailDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 英雄查询服务实现类
 */
@Service
public class HeroQueryServiceImpl implements HeroQueryService {

    @Autowired
    private SysHeroService sysHeroService;

    @Autowired
    private SysHeroSkillPlanService sysHeroSkillPlanService;

    @Autowired
    private SysHeroEquipmentPlanService sysHeroEquipmentPlanService;

    @Autowired
    private SysHeroCooperateService sysHeroCooperateService;

    @Autowired
    private SysHeroRestraintService sysHeroRestraintService;

    @Override
    public List<SysHero> getHeroList(String attribute, String camp) {
        QueryWrapper<SysHero> wrapper = new QueryWrapper<>();
        wrapper.eq("attribute", attribute);
        wrapper.eq("camp", camp);
        return sysHeroService.list(wrapper);
    }

    @Override
    public HeroDetailDTO getHeroDetail(Integer heroId) {
        // 1. 获取英雄基础信息
        SysHero hero = sysHeroService.getById(heroId);
        if (hero == null) {
            return null;
        }

        // 2. 创建DTO并设置基础信息
        HeroDetailDTO dto = new HeroDetailDTO();
        dto.setId(hero.getId());
        dto.setName(hero.getName());
        dto.setNickName(hero.getNickName());
        dto.setImg(hero.getImg());
        dto.setAttribute(hero.getAttribute());
        dto.setCamp(hero.getCamp());
        dto.setSkillInfo(hero.getSkillInfo());
        dto.setEquipmentInfo(hero.getEquipmentInfo());
        dto.setInfo(hero.getInfo());

        // 3. 获取技能加点方案
        QueryWrapper<SysHeroSkillPlan> skillWrapper = new QueryWrapper<>();
        skillWrapper.eq("hero_id", heroId);
        skillWrapper.orderByAsc("level");
        List<SysHeroSkillPlan> skillPlans = sysHeroSkillPlanService.list(skillWrapper);
        List<String> skillPlanImages = skillPlans.stream()
                .map(SysHeroSkillPlan::getSkillImg)
                .collect(Collectors.toList());
        dto.setSkillPlan(skillPlanImages);

        // 4. 获取装备方案
        QueryWrapper<SysHeroEquipmentPlan> equipmentWrapper = new QueryWrapper<>();
        equipmentWrapper.eq("hero_id", heroId);
        equipmentWrapper.orderByAsc("plan_type", "sort_order");
        List<SysHeroEquipmentPlan> equipmentPlans = sysHeroEquipmentPlanService.list(equipmentWrapper);

        Map<String, List<HeroDetailDTO.EquipmentItem>> equipmentPlanMap = equipmentPlans.stream()
                .collect(Collectors.groupingBy(
                    SysHeroEquipmentPlan::getPlanType,
                    LinkedHashMap::new,
                    Collectors.mapping(plan -> {
                        HeroDetailDTO.EquipmentItem item = new HeroDetailDTO.EquipmentItem();
                        item.setImg(plan.getEquipmentImg());
                        item.setSortOrder(plan.getSortOrder());
                        item.setDescription(plan.getDescription());
                        return item;
                    }, Collectors.toList())
                ));
        dto.setEquipmentPlan(equipmentPlanMap);

        // 5. 获取配合英雄
        QueryWrapper<SysHeroCooperate> cooperateWrapper = new QueryWrapper<>();
        cooperateWrapper.eq("hero_id", heroId);
        List<SysHeroCooperate> cooperateList = sysHeroCooperateService.list(cooperateWrapper);
        List<HeroDetailDTO.HeroRelation> cooperateRelations = cooperateList.stream()
                .map(cooperate -> {
                    HeroDetailDTO.HeroRelation relation = new HeroDetailDTO.HeroRelation();
                    relation.setImg(cooperate.getCooperateHeroImg());
                    relation.setName(cooperate.getCooperateHeroName());
                    return relation;
                })
                .collect(Collectors.toList());
        dto.setCooperate(cooperateRelations);

        // 6. 获取克制英雄
        QueryWrapper<SysHeroRestraint> restraintWrapper = new QueryWrapper<>();
        restraintWrapper.eq("hero_id", heroId);
        List<SysHeroRestraint> restraintList = sysHeroRestraintService.list(restraintWrapper);
        List<HeroDetailDTO.HeroRelation> restraintRelations = restraintList.stream()
                .map(restraint -> {
                    HeroDetailDTO.HeroRelation relation = new HeroDetailDTO.HeroRelation();
                    relation.setImg(restraint.getRestraintHeroImg());
                    relation.setName(restraint.getRestraintHeroName());
                    return relation;
                })
                .collect(Collectors.toList());
        dto.setRestraint(restraintRelations);

        return dto;
    }
}
