package com.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 英雄装备方案实体类
 */
@Data
@TableName("sys_hero_equipment_plan")
public class SysHeroEquipmentPlan {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    private Integer heroId;
    
    private String planType;
    
    private String equipmentImg;
    
    private Integer sortOrder;
    
    private String description;
    
    private LocalDateTime createTime;
}