<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DOTA英雄管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .hero-list {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .hero-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .hero-item:hover {
            background-color: #f8f9fa;
        }
        
        .hero-item.active {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        
        .hero-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin-right: 15px;
            object-fit: cover;
        }
        
        .hero-info h3 {
            margin-bottom: 5px;
            color: #333;
        }
        
        .hero-info p {
            color: #666;
            font-size: 14px;
        }
        
        .hero-detail {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: none;
        }
        
        .detail-section {
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .section-header {
            background-color: #f5f5f5;
            padding: 15px;
            font-weight: bold;
            color: #333;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .section-content {
            padding: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        
        .skill-plan-container,
        .equipment-container,
        .relation-container {
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 15px;
            background-color: #fafafa;
        }
        
        .skill-item,
        .equipment-item,
        .relation-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border: 1px solid #e0e0e0;
        }
        
        .skill-item input,
        .equipment-item input,
        .relation-item input {
            flex: 1;
            margin-right: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #0056b3;
        }
        
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background-color: #1e7e34;
        }
        
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #c82333;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #545b62;
        }
        
        .equipment-type {
            font-weight: bold;
            color: #495057;
            margin-top: 20px;
            margin-bottom: 10px;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 4px;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        
        .success {
            color: #155724;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        
        .two-column {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
        }
        
        @media (max-width: 768px) {
            .two-column {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 DOTA英雄管理系统</h1>
            <p>查看和编辑英雄的详细信息</p>
        </div>
        
        <div class="two-column">
            <!-- 英雄列表 -->
            <div class="hero-list">
                <h2>英雄列表</h2>
                <div id="heroListContainer">
                    <div class="loading">加载中...</div>
                </div>
            </div>
            
            <!-- 英雄详情编辑 -->
            <div class="hero-detail" id="heroDetail">
                <div id="messageContainer"></div>
                
                <!-- 基础信息 -->
                <div class="detail-section">
                    <div class="section-header">基础信息</div>
                    <div class="section-content">
                        <div class="form-group">
                            <label>英雄名称</label>
                            <input type="text" id="heroName" placeholder="请输入英雄名称">
                        </div>
                        <div class="form-group">
                            <label>英雄昵称</label>
                            <input type="text" id="heroNickName" placeholder="请输入英雄昵称">
                        </div>
                        <div class="form-group">
                            <label>头像链接</label>
                            <input type="text" id="heroImg" placeholder="请输入头像链接">
                        </div>
                        <div class="form-group">
                            <label>属性类型</label>
                            <select id="heroAttribute">
                                <option value="0">智力</option>
                                <option value="1">敏捷</option>
                                <option value="2">力量</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>阵营</label>
                            <select id="heroCamp">
                                <option value="0">中立</option>
                                <option value="1">近卫</option>
                                <option value="2">天灾</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- 技能信息 -->
                <div class="detail-section">
                    <div class="section-header">技能信息</div>
                    <div class="section-content">
                        <div class="form-group">
                            <label>技能说明</label>
                            <textarea id="skillInfo" placeholder="请输入技能说明"></textarea>
                        </div>
                        <div class="form-group">
                            <label>技能加点方案</label>
                            <div class="skill-plan-container" id="skillPlanContainer">
                                <!-- 动态生成技能加点项 -->
                            </div>
                            <button type="button" class="btn btn-secondary" onclick="addSkillItem()">添加技能</button>
                        </div>
                    </div>
                </div>
                
                <!-- 装备信息 -->
                <div class="detail-section">
                    <div class="section-header">装备信息</div>
                    <div class="section-content">
                        <div class="form-group">
                            <label>装备说明</label>
                            <textarea id="equipmentInfo" placeholder="请输入装备说明"></textarea>
                        </div>
                        <div class="form-group">
                            <label>装备方案</label>
                            <div class="equipment-container" id="equipmentContainer">
                                <!-- 动态生成装备方案 -->
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 英雄关系 -->
                <div class="detail-section">
                    <div class="section-header">英雄关系</div>
                    <div class="section-content">
                        <div class="form-group">
                            <label>配合英雄</label>
                            <div class="relation-container" id="cooperateContainer">
                                <!-- 动态生成配合英雄 -->
                            </div>
                            <button type="button" class="btn btn-secondary" onclick="addCooperateItem()">添加配合英雄</button>
                        </div>
                        <div class="form-group">
                            <label>克制英雄</label>
                            <div class="relation-container" id="restraintContainer">
                                <!-- 动态生成克制英雄 -->
                            </div>
                            <button type="button" class="btn btn-secondary" onclick="addRestraintItem()">添加克制英雄</button>
                        </div>
                    </div>
                </div>
                
                <!-- 详细介绍 -->
                <div class="detail-section">
                    <div class="section-header">详细介绍</div>
                    <div class="section-content">
                        <div class="form-group">
                            <label>英雄介绍</label>
                            <textarea id="heroInfo" placeholder="请输入英雄详细介绍"></textarea>
                        </div>
                    </div>
                </div>
                
                <!-- 操作按钮 -->
                <div style="text-align: center; margin-top: 30px;">
                    <button type="button" class="btn btn-success" onclick="saveHero()" style="margin-right: 10px;">保存修改</button>
                    <button type="button" class="btn btn-secondary" onclick="resetForm()">重置</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentHeroId = null;
        let currentHeroData = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadHeroesList();
        });

        // 加载英雄列表
        async function loadHeroesList() {
            try {
                console.log('开始加载英雄列表...');
                const response = await fetch('/manage/dota/api/heroes');
                console.log('API响应状态:', response.status);
                
                const result = await response.json();
                console.log('API响应数据:', result);
                
                if (result.code === 200) {
                    if (result.data && result.data.length > 0) {
                        renderHeroesList(result.data);
                    } else {
                        document.getElementById('heroListContainer').innerHTML = 
                            '<div class="loading">暂无英雄数据，请先同步数据</div>';
                    }
                } else {
                    showError('加载英雄列表失败: ' + (result.message || '未知错误'));
                }
            } catch (error) {
                console.error('加载英雄列表错误:', error);
                showError('加载英雄列表失败: ' + error.message);
                document.getElementById('heroListContainer').innerHTML = 
                    '<div class="error">无法连接到服务器，请检查网络连接</div>';
            }
        }

        // 渲染英雄列表
        function renderHeroesList(heroes) {
            const container = document.getElementById('heroListContainer');
            
            if (!heroes || heroes.length === 0) {
                container.innerHTML = '<div class="loading">暂无英雄数据</div>';
                return;
            }
            
            const html = heroes.map(hero => `
                <div class="hero-item" onclick="selectHero(${hero.id})">
                    <img src="${hero.img || '/default-avatar.png'}" alt="${hero.name || '未知英雄'}" class="hero-avatar" onerror="this.src='/default-avatar.png'">
                    <div class="hero-info">
                        <h3>${hero.name || '未知英雄'}</h3>
                        <p>${hero.nickName || '??'} | ${getAttributeName(hero.attribute)} | ${getCampName(hero.camp)}</p>
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }

        // 选择英雄
        async function selectHero(heroId) {
            // 更新选中状态
            document.querySelectorAll('.hero-item').forEach(item => {
                item.classList.remove('active');
            });
            event.currentTarget.classList.add('active');
            
            currentHeroId = heroId;
            
            try {
                const response = await fetch(`/manage/dota/api/hero/${heroId}`);
                const result = await response.json();
                
                if (result.code === 200) {
                    currentHeroData = result.data;
                    renderHeroDetail(result.data);
                    document.getElementById('heroDetail').style.display = 'block';
                } else {
                    showError('加载英雄详情失败: ' + result.message);
                }
            } catch (error) {
                showError('加载英雄详情失败: ' + error.message);
            }
        }

        // 渲染英雄详情
        function renderHeroDetail(hero) {
            // 基础信息
            document.getElementById('heroName').value = hero.name || '';
            document.getElementById('heroNickName').value = hero.nickName || '';
            document.getElementById('heroImg').value = hero.img || '';
            document.getElementById('heroAttribute').value = hero.attribute || 0;
            document.getElementById('heroCamp').value = hero.camp || 0;
            document.getElementById('skillInfo').value = hero.skillInfo || '';
            document.getElementById('equipmentInfo').value = hero.equipmentInfo || '';
            document.getElementById('heroInfo').value = hero.info || '';
            
            // 技能加点方案
            renderSkillPlan(hero.skillPlan || []);
            
            // 装备方案
            renderEquipmentPlan(hero.equipmentPlan || {});
            
            // 配合英雄
            renderCooperateHeroes(hero.cooperate || []);
            
            // 克制英雄
            renderRestraintHeroes(hero.restraint || []);
        }

        // 渲染技能加点方案
        function renderSkillPlan(skillPlan) {
            const container = document.getElementById('skillPlanContainer');
            const html = skillPlan.map((skill, index) => `
                <div class="skill-item">
                    <span style="width: 60px; text-align: center;">Lv${index + 1}</span>
                    <input type="text" value="${skill}" placeholder="技能图片链接" onchange="updateSkillPlan()">
                    <button type="button" class="btn btn-danger" onclick="removeSkillItem(this)">删除</button>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }

        // 渲染装备方案
        function renderEquipmentPlan(equipmentPlan) {
            const container = document.getElementById('equipmentContainer');
            let html = '';
            
            const equipmentTypes = ['出门装', '中期核心装', '后期神装'];
            
            equipmentTypes.forEach(type => {
                html += `<div class="equipment-type">${type}</div>`;
                const items = equipmentPlan[type] || [];
                
                items.forEach((item, index) => {
                    html += `
                        <div class="equipment-item" data-type="${type}">
                            <input type="text" value="${item.img}" placeholder="装备图片链接" onchange="updateEquipmentPlan()">
                            <input type="text" value="${item.description || ''}" placeholder="装备说明" onchange="updateEquipmentPlan()">
                            <button type="button" class="btn btn-danger" onclick="removeEquipmentItem(this)">删除</button>
                        </div>
                    `;
                });
                
                html += `<button type="button" class="btn btn-secondary" onclick="addEquipmentItem('${type}')">添加${type}</button>`;
            });
            
            container.innerHTML = html;
        }

        // 渲染配合英雄
        function renderCooperateHeroes(cooperate) {
            const container = document.getElementById('cooperateContainer');
            const html = cooperate.map(hero => `
                <div class="relation-item">
                    <input type="text" value="${hero.img}" placeholder="英雄图片链接" onchange="updateCooperate()">
                    <input type="text" value="${hero.name}" placeholder="英雄名称" onchange="updateCooperate()">
                    <button type="button" class="btn btn-danger" onclick="removeRelationItem(this)">删除</button>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }

        // 渲染克制英雄
        function renderRestraintHeroes(restraint) {
            const container = document.getElementById('restraintContainer');
            const html = restraint.map(hero => `
                <div class="relation-item">
                    <input type="text" value="${hero.img}" placeholder="英雄图片链接" onchange="updateRestraint()">
                    <input type="text" value="${hero.name}" placeholder="英雄名称" onchange="updateRestraint()">
                    <button type="button" class="btn btn-danger" onclick="removeRelationItem(this)">删除</button>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }

        // 添加技能项
        function addSkillItem() {
            const container = document.getElementById('skillPlanContainer');
            const level = container.children.length + 1;
            const html = `
                <div class="skill-item">
                    <span style="width: 60px; text-align: center;">Lv${level}</span>
                    <input type="text" value="" placeholder="技能图片链接" onchange="updateSkillPlan()">
                    <button type="button" class="btn btn-danger" onclick="removeSkillItem(this)">删除</button>
                </div>
            `;
            container.insertAdjacentHTML('beforeend', html);
        }

        // 删除技能项
        function removeSkillItem(button) {
            button.parentElement.remove();
            updateSkillLevels();
        }

        // 更新技能等级显示
        function updateSkillLevels() {
            const skillItems = document.querySelectorAll('#skillPlanContainer .skill-item');
            skillItems.forEach((item, index) => {
                const span = item.querySelector('span');
                span.textContent = `Lv${index + 1}`;
            });
        }

        // 添加装备项
        function addEquipmentItem(type) {
            const container = document.getElementById('equipmentContainer');
            const typeElement = Array.from(container.children).find(el => el.textContent === type);
            const html = `
                <div class="equipment-item" data-type="${type}">
                    <input type="text" value="" placeholder="装备图片链接" onchange="updateEquipmentPlan()">
                    <input type="text" value="" placeholder="装备说明" onchange="updateEquipmentPlan()">
                    <button type="button" class="btn btn-danger" onclick="removeEquipmentItem(this)">删除</button>
                </div>
            `;
            
            // 找到对应类型的按钮，在其前面插入
            const buttons = container.querySelectorAll('button');
            for (let button of buttons) {
                if (button.textContent.includes(type)) {
                    button.insertAdjacentHTML('beforebegin', html);
                    break;
                }
            }
        }

        // 删除装备项
        function removeEquipmentItem(button) {
            button.parentElement.remove();
        }

        // 添加配合英雄
        function addCooperateItem() {
            const container = document.getElementById('cooperateContainer');
            const html = `
                <div class="relation-item">
                    <input type="text" value="" placeholder="英雄图片链接" onchange="updateCooperate()">
                    <input type="text" value="" placeholder="英雄名称" onchange="updateCooperate()">
                    <button type="button" class="btn btn-danger" onclick="removeRelationItem(this)">删除</button>
                </div>
            `;
            container.insertAdjacentHTML('beforeend', html);
        }

        // 添加克制英雄
        function addRestraintItem() {
            const container = document.getElementById('restraintContainer');
            const html = `
                <div class="relation-item">
                    <input type="text" value="" placeholder="英雄图片链接" onchange="updateRestraint()">
                    <input type="text" value="" placeholder="英雄名称" onchange="updateRestraint()">
                    <button type="button" class="btn btn-danger" onclick="removeRelationItem(this)">删除</button>
                </div>
            `;
            container.insertAdjacentHTML('beforeend', html);
        }

        // 删除关系项
        function removeRelationItem(button) {
            button.parentElement.remove();
        }

        // 保存英雄信息
        async function saveHero() {
            if (!currentHeroId) {
                showError('请先选择一个英雄');
                return;
            }

            const updateData = {
                name: document.getElementById('heroName').value,
                nickName: document.getElementById('heroNickName').value,
                img: document.getElementById('heroImg').value,
                attribute: parseInt(document.getElementById('heroAttribute').value),
                camp: parseInt(document.getElementById('heroCamp').value),
                skillInfo: document.getElementById('skillInfo').value,
                equipmentInfo: document.getElementById('equipmentInfo').value,
                info: document.getElementById('heroInfo').value,
                skillPlan: getSkillPlanData(),
                equipmentPlan: getEquipmentPlanData(),
                cooperate: getCooperateData(),
                restraint: getRestraintData()
            };

            try {
                const response = await fetch(`/manage/dota/api/hero/${currentHeroId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(updateData)
                });

                const result = await response.json();
                
                if (result.code === 200) {
                    showSuccess('英雄信息保存成功！');
                    // 重新加载英雄列表
                    loadHeroesList();
                } else {
                    showError('保存失败: ' + result.message);
                }
            } catch (error) {
                showError('保存失败: ' + error.message);
            }
        }

        // 获取技能加点数据
        function getSkillPlanData() {
            const skillItems = document.querySelectorAll('#skillPlanContainer .skill-item input');
            return Array.from(skillItems).map(input => input.value).filter(value => value.trim());
        }

        // 获取装备方案数据
        function getEquipmentPlanData() {
            const equipmentPlan = {};
            const equipmentItems = document.querySelectorAll('#equipmentContainer .equipment-item');
            
            equipmentItems.forEach(item => {
                const type = item.getAttribute('data-type');
                const inputs = item.querySelectorAll('input');
                const img = inputs[0].value;
                const description = inputs[1].value;
                
                if (img.trim()) {
                    if (!equipmentPlan[type]) {
                        equipmentPlan[type] = [];
                    }
                    equipmentPlan[type].push({
                        img: img,
                        description: description,
                        sortOrder: equipmentPlan[type].length + 1
                    });
                }
            });
            
            return equipmentPlan;
        }

        // 获取配合英雄数据
        function getCooperateData() {
            const cooperateItems = document.querySelectorAll('#cooperateContainer .relation-item');
            return Array.from(cooperateItems).map(item => {
                const inputs = item.querySelectorAll('input');
                return {
                    img: inputs[0].value,
                    name: inputs[1].value
                };
            }).filter(item => item.img.trim() || item.name.trim());
        }

        // 获取克制英雄数据
        function getRestraintData() {
            const restraintItems = document.querySelectorAll('#restraintContainer .relation-item');
            return Array.from(restraintItems).map(item => {
                const inputs = item.querySelectorAll('input');
                return {
                    img: inputs[0].value,
                    name: inputs[1].value
                };
            }).filter(item => item.img.trim() || item.name.trim());
        }

        // 重置表单
        function resetForm() {
            if (currentHeroData) {
                renderHeroDetail(currentHeroData);
                showSuccess('表单已重置');
            }
        }

        // 显示错误信息
        function showError(message) {
            const container = document.getElementById('messageContainer');
            container.innerHTML = `<div class="error">${message}</div>`;
            setTimeout(() => {
                container.innerHTML = '';
            }, 5000);
        }

        // 显示成功信息
        function showSuccess(message) {
            const container = document.getElementById('messageContainer');
            container.innerHTML = `<div class="success">${message}</div>`;
            setTimeout(() => {
                container.innerHTML = '';
            }, 3000);
        }

        // 获取属性名称
        function getAttributeName(attribute) {
            const names = ['智力', '敏捷', '力量'];
            return names[attribute] || '未知';
        }

        // 获取阵营名称
        function getCampName(camp) {
            const names = ['中立', '近卫', '天灾'];
            return names[camp] || '未知';
        }

        // 更新相关函数（占位符，实际不需要实现）
        function updateSkillPlan() {}
        function updateEquipmentPlan() {}
        function updateCooperate() {}
        function updateRestraint() {}
    </script>
</body>
</html>