package com.miniapp.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.model.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 英雄数据更新服务
 */
@Service
public class HeroDataUpdateService {

    @Autowired
    private SysHeroService sysHeroService;

    @Autowired
    private SysHeroSkillPlanService sysHeroSkillPlanService;

    @Autowired
    private SysHeroEquipmentPlanService sysHeroEquipmentPlanService;

    @Autowired
    private SysHeroCooperateService sysHeroCooperateService;

    @Autowired
    private SysHeroRestraintService sysHeroRestraintService;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 根据英雄ID从Redis获取数据并更新到数据库
     * @param heroId 英雄ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateHeroDataFromRedis(Integer heroId) {
        try {
            // 从Redis获取英雄详情数据
            String cacheKey = "dota:hero:detail:" + heroId;
            SysHero heroFromRedis = (SysHero) redisTemplate.opsForValue().get(cacheKey);

            if (heroFromRedis == null) {
                throw new RuntimeException("Redis中未找到英雄ID为 " + heroId + " 的数据");
            }

            // 1. 更新英雄基础信息
            updateHeroBasicInfo(heroId, heroFromRedis);

            // 2. 更新技能加点方案
//            if (heroFromRedis.getSkillPlan() != null) {
//                updateSkillPlan(heroId, heroFromRedis.getSkillPlan());
//            }
//
//            // 3. 更新装备方案
//            if (heroFromRedis.getEquipmentPlan() != null) {
//                updateEquipmentPlan(heroId, heroFromRedis.getEquipmentPlan());
//            }
//
//            // 4. 更新配合英雄
//            if (heroFromRedis.getCooperate() != null) {
//                updateCooperateHeroes(heroId, heroFromRedis.getCooperate());
//            }
//
//            // 5. 更新克制英雄
//            if (heroFromRedis.getRestraint() != null) {
//                updateRestraintHeroes(heroId, heroFromRedis.getRestraint());
//            }

        } catch (Exception e) {
            throw new RuntimeException("从Redis更新英雄数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据JSON数据更新英雄信息
     *
     * @param heroId   英雄ID
     * @param jsonData JSON数据字符串
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateHeroData(Integer heroId, String jsonData) {
        try {
            // 解析JSON数据
            JSONArray jsonArray = JSON.parseArray(jsonData);
            JSONObject heroData = jsonArray.getJSONArray(1).getJSONObject(0);

            // 1. 更新英雄基础信息
            updateHeroBasicInfo(heroId, heroData);

            // 2. 更新技能加点方案
            updateSkillPlan(heroId, heroData.getString("skillPlan"));

            // 3. 更新装备方案
            updateEquipmentPlan(heroId, heroData.getString("equipmentPlan"));

            // 4. 更新配合英雄
            updateCooperateHeroes(heroId, heroData.getString("cooperate"));

            // 5. 更新克制英雄
            updateRestraintHeroes(heroId, heroData.getString("restraint"));

        } catch (Exception e) {
            throw new RuntimeException("更新英雄数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新英雄基础信息 - 从Redis的SysHero对象
     */
    private void updateHeroBasicInfo(Integer heroId, SysHero heroFromRedis) {
        SysHero hero = sysHeroService.getById(heroId);
        if (hero == null) {
            hero = new SysHero();
            hero.setId(heroId);
        }

        hero.setName(heroFromRedis.getName());
        hero.setNickName(heroFromRedis.getNickName());
        hero.setImg(heroFromRedis.getImg());
        hero.setAttribute(heroFromRedis.getAttribute());
        hero.setCamp(heroFromRedis.getCamp());
        hero.setSkillInfo(heroFromRedis.getSkillInfo());
        hero.setEquipmentInfo(heroFromRedis.getEquipmentInfo());
        hero.setInfo(heroFromRedis.getInfo());

        sysHeroService.saveOrUpdate(hero);
    }

    /**
     * 更新英雄基础信息 - 从JSON对象
     */
    private void updateHeroBasicInfo(Integer heroId, JSONObject heroData) {
        SysHero hero = sysHeroService.getById(heroId);
        if (hero == null) {
            hero = new SysHero();
            hero.setId(heroId);
        }

        hero.setName(heroData.getString("name"));
        hero.setNickName(heroData.getString("nickName"));
        hero.setImg(heroData.getString("img"));
        hero.setAttribute(heroData.getInteger("attribute"));
        hero.setCamp(heroData.getInteger("camp"));
        hero.setSkillInfo(heroData.getString("skillInfo"));
        hero.setEquipmentInfo(heroData.getString("equipmentInfo"));
        hero.setInfo(heroData.getString("info"));

        sysHeroService.saveOrUpdate(hero);
    }

    /**
     * 更新技能加点方案
     */
    private void updateSkillPlan(Integer heroId, String skillPlanJson) {
        // 先删除原有的技能加点方案
        QueryWrapper<SysHeroSkillPlan> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.eq("hero_id", heroId);
        sysHeroSkillPlanService.remove(deleteWrapper);

        // 解析并插入新的技能加点方案
        JSONArray skillArray = JSON.parseArray(skillPlanJson);
        for (int i = 0; i < skillArray.size(); i++) {
            SysHeroSkillPlan skillPlan = new SysHeroSkillPlan();
            skillPlan.setHeroId(heroId);
            skillPlan.setLevel(i + 1); // 等级从1开始
            skillPlan.setSkillImg(skillArray.getString(i));
            sysHeroSkillPlanService.save(skillPlan);
        }
    }

    /**
     * 更新装备方案
     */
    private void updateEquipmentPlan(Integer heroId, String equipmentPlanJson) {
        // 先删除原有的装备方案
        QueryWrapper<SysHeroEquipmentPlan> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.eq("hero_id", heroId);
        sysHeroEquipmentPlanService.remove(deleteWrapper);

        // 解析装备方案JSON
        JSONObject equipmentObj = JSON.parseObject(equipmentPlanJson);

        // 处理每种装备类型
        for (String planType : equipmentObj.keySet()) {
            JSONArray planArray = equipmentObj.getJSONArray(planType);

            // 第一个数组是装备图片列表
            JSONArray equipmentImages = planArray.getJSONArray(0);
            // 第二个数组是描述信息
            String description = planArray.size() > 1 ? planArray.getJSONArray(1).getString(0) : null;

            // 插入装备数据
            for (int i = 0; i < equipmentImages.size(); i++) {
                SysHeroEquipmentPlan equipmentPlan = new SysHeroEquipmentPlan();
                equipmentPlan.setHeroId(heroId);
                equipmentPlan.setPlanType(planType);
                equipmentPlan.setEquipmentImg(equipmentImages.getString(i));
                equipmentPlan.setSortOrder(i + 1);
                // 只在第一个装备上设置描述
                if (i == 0 && description != null) {
                    equipmentPlan.setDescription(description);
                }
                sysHeroEquipmentPlanService.save(equipmentPlan);
            }
        }
    }

    /**
     * 更新配合英雄
     */
    private void updateCooperateHeroes(Integer heroId, String cooperateJson) {
        // 先删除原有的配合关系
        QueryWrapper<SysHeroCooperate> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.eq("hero_id", heroId);
        sysHeroCooperateService.remove(deleteWrapper);

        // 解析并插入新的配合关系
        JSONArray cooperateArray = JSON.parseArray(cooperateJson);
        for (int i = 0; i < cooperateArray.size(); i++) {
            SysHeroCooperate cooperate = new SysHeroCooperate();
            cooperate.setHeroId(heroId);
            cooperate.setCooperateHeroImg(cooperateArray.getString(i));
            cooperate.setCooperateHeroName(extractHeroNameFromPath(cooperateArray.getString(i)));
            sysHeroCooperateService.save(cooperate);
        }
    }

    /**
     * 更新克制英雄
     */
    private void updateRestraintHeroes(Integer heroId, String restraintJson) {
        // 先删除原有的克制关系
        QueryWrapper<SysHeroRestraint> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.eq("hero_id", heroId);
        sysHeroRestraintService.remove(deleteWrapper);

        // 解析并插入新的克制关系
        JSONArray restraintArray = JSON.parseArray(restraintJson);
        for (int i = 0; i < restraintArray.size(); i++) {
            SysHeroRestraint restraint = new SysHeroRestraint();
            restraint.setHeroId(heroId);
            restraint.setRestraintHeroImg(restraintArray.getString(i));
            restraint.setRestraintHeroName(extractHeroNameFromPath(restraintArray.getString(i)));
            sysHeroRestraintService.save(restraint);
        }
    }

    /**
     * 从图片路径中提取英雄名称
     */
    private String extractHeroNameFromPath(String imagePath) {
        try {
            // 从路径中提取英雄名称，例如：/VS/VS.gif -> VS
            String[] parts = imagePath.split("/");
            for (int i = parts.length - 1; i >= 0; i--) {
                if (parts[i].contains(".")) {
                    String fileName = parts[i].substring(0, parts[i].lastIndexOf("."));
                    if (!fileName.isEmpty()) {
                        return fileName;
                    }
                }
            }
            return "未知英雄";
        } catch (Exception e) {
            return "未知英雄";
        }
    }
}
