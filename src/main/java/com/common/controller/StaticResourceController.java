package com.common.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 静态资源控制器 - 处理全局静态资源请求
 */
@Controller
public class StaticResourceController {

    /**
     * 全局默认头像处理
     */
    @GetMapping("/default-avatar.png")
    public void globalDefaultAvatar(HttpServletResponse response) throws IOException {
        response.setContentType("image/svg+xml");
        response.setCharacterEncoding("UTF-8");
        
        String svg = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
                "<svg width=\"50\" height=\"50\" viewBox=\"0 0 50 50\" xmlns=\"http://www.w3.org/2000/svg\">" +
                "<rect width=\"50\" height=\"50\" fill=\"#e0e0e0\"/>" +
                "<circle cx=\"25\" cy=\"20\" r=\"8\" fill=\"#999\"/>" +
                "<path d=\"M10 40 Q25 30 40 40 L40 50 L10 50 Z\" fill=\"#999\"/>" +
                "<text x=\"25\" y=\"45\" text-anchor=\"middle\" font-size=\"8\" fill=\"#666\">HERO</text>" +
                "</svg>";
        
        response.getWriter().write(svg);
    }
}