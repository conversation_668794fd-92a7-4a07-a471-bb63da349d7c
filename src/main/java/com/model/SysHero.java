package com.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;

@Data
@TableName("sys_hero")  // 指定表名（含数据库名）
public class SysHero implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)  // 主键自增
    private Integer id;

    @TableField("name")  // 对应数据库字段
    private String name;

    @TableField("nick_name")  // 数据库字段名（下划线转驼峰）
    private String nickName;


    @TableField("skill_info")
    private String skillInfo;

    @TableField("equipment_info")
    private String equipmentInfo;

    @TableField("info")
    private String info;

    @TableField("img")
    private String img;

    @TableField("attribute")
    private Integer attribute;

    @TableField("camp")
    private Integer camp;
}
