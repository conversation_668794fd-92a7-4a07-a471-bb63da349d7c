package com.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Map;

/**
 * <AUTHOR> Edyx
 * @CreateTime : 2022/8/8 17:04
 * @Version : 1.0.0
 * @Description :
 */
public class HttpUtil {
    /**
     * @Description: post方式获取访问url接口
     * <AUTHOR>
     * @date 2022/4/29 9:40
     * @param url : 地址
     * @param param : 参数
     * @param token : url携带的headers（非必须）
     * @return java.lang.Object
     */
    public static Object requestPostUrl(String url, Map<String, Object> param, String token) throws Exception{

        InputStream is = null;
        String body = null;
        StringBuilder   res=new StringBuilder();
        HttpPost httpPost = new HttpPost(url);
        httpPost.addHeader("Content-Type", "application/json");
        if(token!=null){
            httpPost.addHeader("Authorization",token);
        }

        // 设置请求的参数
        JSONObject jsonParam = new JSONObject();

        if (param != null) {
            param.forEach((k,v)-> jsonParam.put(k,v));
        }

        StringEntity stringEntity = new StringEntity(jsonParam.toString(), "utf-8");

        stringEntity.setContentEncoding("UTF-8");
        stringEntity.setContentType("application/json");
        httpPost.setEntity(stringEntity);

        RequestConfig config = RequestConfig.custom().setConnectTimeout(5000).build();

        httpPost.setConfig(config);

        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = httpClient.execute(httpPost);
        HttpEntity entity = response.getEntity();
        if(entity != null){
            is = entity.getContent();
            //转换为字节输入流
            BufferedReader br = new BufferedReader(new InputStreamReader(is, Consts.UTF_8));
            while((body=br.readLine()) != null){
                res.append(body);
            }
        }
        Object jsonMap = JSON.parse(res.toString());

        return jsonMap;
    }


    /**
     * @Description: get方式
     * <AUTHOR>
     * @date 2022/5/6 9:50
     * @param url 地址
     * @param token url携带的headers（非必须）
     * @return java.lang.Object
     */
    public static Object requestGetUrl(String url, String token) throws Exception{

        InputStream is = null;
        String body = null;
        StringBuilder  res=new StringBuilder();
        // 设置完整的url
        URIBuilder uriBuilder = null;
        uriBuilder = new URIBuilder(url);
        //添加参数
        //for (Map.Entry<String, String> entry : param.entrySet()) {
        //    uriBuilder.setParameter(entry.getKey(),entry.getValue());
        //}Map<String, String> param,
        HttpGet httpGet = new HttpGet(uriBuilder.build());
        httpGet.addHeader("Content-Type", "application/json");
        if(token!=null){
            httpGet.addHeader("Authorization",token);
        }


        RequestConfig config = RequestConfig.custom().setConnectTimeout(5000).build();

        httpGet.setConfig(config);

        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = httpClient.execute(httpGet);
        HttpEntity entity = response.getEntity();
        if(entity != null){
            is = entity.getContent();
            //转换为字节输入流
            BufferedReader br = new BufferedReader(new InputStreamReader(is, Consts.UTF_8));
            while((body=br.readLine()) != null){
                res.append(body);
            }
        }


        Object jsonMap = JSON.parse(res.toString());


        return jsonMap;
    }


}
