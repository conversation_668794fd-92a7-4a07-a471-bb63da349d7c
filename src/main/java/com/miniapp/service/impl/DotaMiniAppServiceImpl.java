package com.miniapp.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miniapp.client.DotaMiniClient;
import com.miniapp.mapper.DotaMiniAppMapper;
import com.miniapp.service.DotaMiniAppService;
import com.model.BatchDownloadFile;
import com.model.BatchDownloadFileResponse;
import com.model.SysHero;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

import static com.miniapp.client.DotaMiniClient.env;

/**
 * @Title: GoodsServiceImpl
 * <AUTHOR>
 * @Package com.miniapp.service.impl
 * @Date 2022/12/5 15:30
 * @description: 特惠流量包商品列表接口实现类
 */
@Service
public class DotaMiniAppServiceImpl extends ServiceImpl<DotaMiniAppMapper, SysHero> implements DotaMiniAppService {

    @Autowired
    private DotaMiniAppMapper dotaMiniAppMapper;

    @Autowired
    private DotaMiniClient dotaMiniClient;


    @Override
    public SysHero getDetailById(Integer heroId, String accessToken) {
        SysHero sysHero = dotaMiniAppMapper.selectById(heroId);
        sysHero.setImg(getHeroImgTempUrl(sysHero.getImg(), accessToken));
//        sysHero.setSkillPlan(getGeneralTempUrl(sysHero.getSkillPlan(), accessToken));
//        sysHero.setEquipmentPlan(getEquipmentPlanTempUrl(sysHero.getEquipmentPlan(), accessToken));
//        sysHero.setCooperate(getGeneralTempUrl(sysHero.getCooperate(), accessToken));
//        sysHero.setRestraint(getGeneralTempUrl(sysHero.getRestraint(), accessToken));
        return sysHero;
    }

    /**
     * 处理通用图片列表
     *
     * @param imgList
     * @param accessToken
     * @return
     */
    private String getGeneralTempUrl(String imgList, String accessToken) {
        if (imgList == null || imgList.isEmpty()) {
            return null;
        }
        try {
            com.alibaba.fastjson.JSONArray jsonArray = com.alibaba.fastjson.JSON.parseArray(imgList);
            BatchDownloadFile batchDownloadFile = new BatchDownloadFile();
            batchDownloadFile.setEnv(env);
            List<BatchDownloadFile.FileList> fileList = new ArrayList<>();

            // 为每个文件ID创建请求项
            for (int i = 0; i < jsonArray.size(); i++) {
                BatchDownloadFile.FileList imgFile = new BatchDownloadFile.FileList();
                imgFile.setFileid(jsonArray.getString(i));
                imgFile.setMaxAge(7200);
                fileList.add(imgFile);
            }
            batchDownloadFile.setFileList(fileList);

            BatchDownloadFileResponse response = dotaMiniClient.getTempUrl(accessToken, batchDownloadFile);
            List<BatchDownloadFileResponse.FileList> resultList = response.getFileList();

            if (resultList.isEmpty()) {
                return null;
            }

            // 构建返回的JSON数组，包含所有临时链接
            com.alibaba.fastjson.JSONArray resultArray = new com.alibaba.fastjson.JSONArray();
            for (BatchDownloadFileResponse.FileList result : resultList) {
                if (result.getStatus() == 0) { // 成功状态
                    resultArray.add(result.getDownloadUrl());
                }
            }

            return resultArray.toJSONString();

        } catch (Exception e) {
            // 解析失败时保持原样
        }
        return null;
    }

    /**
     * 处理英雄图片
     *
     * @param img
     * @param accessToken
     * @return
     */
    private String getHeroImgTempUrl(String img, String accessToken) {
        if (img == null || img.isEmpty()) {
            return null;
        }

        BatchDownloadFile batchDownloadFile = new BatchDownloadFile();
        List<BatchDownloadFile.FileList> fileList = new ArrayList<>();
        batchDownloadFile.setEnv(env);

        BatchDownloadFile.FileList imgFile = new BatchDownloadFile.FileList();
        imgFile.setFileid(img);
        imgFile.setMaxAge(7200);
        fileList.add(imgFile);

        batchDownloadFile.setFileList(fileList);

        BatchDownloadFileResponse response = dotaMiniClient.getTempUrl(accessToken, batchDownloadFile);
        List<BatchDownloadFileResponse.FileList> resultList = response.getFileList();

        if (resultList.isEmpty()) {
            return null;
        }

        return resultList.get(0).getDownloadUrl();
    }

    /**
     * 处理技能计划装备图片
     *
     * @param equipmentPlan
     * @param accessToken
     * @return
     */
    private String getEquipmentPlanTempUrl(String equipmentPlan, String accessToken) {
        if (equipmentPlan == null || equipmentPlan.isEmpty()) {
            return null;
        }
        try {
            com.alibaba.fastjson.JSONObject skillObj = com.alibaba.fastjson.JSON.parseObject(equipmentPlan);

            // 遍历每个装备分类
            for (String key : skillObj.keySet()) {
                com.alibaba.fastjson.JSONArray categoryArray = skillObj.getJSONArray(key);
                if (categoryArray != null && categoryArray.size() > 0) {
                    // 获取第一个元素（图片数组）
                    com.alibaba.fastjson.JSONArray imageArray = categoryArray.getJSONArray(0);
                    if (imageArray != null && imageArray.size() > 0) {
                        // 收集所有图片文件ID
                        BatchDownloadFile batchDownloadFile = new BatchDownloadFile();
                        batchDownloadFile.setEnv(env);
                        List<BatchDownloadFile.FileList> fileList = new ArrayList<>();

                        for (int i = 0; i < imageArray.size(); i++) {
                            BatchDownloadFile.FileList imgFile = new BatchDownloadFile.FileList();
                            imgFile.setFileid(imageArray.getString(i));
                            imgFile.setMaxAge(7200);
                            fileList.add(imgFile);
                        }
                        batchDownloadFile.setFileList(fileList);

                        // 获取临时链接
                        BatchDownloadFileResponse response = dotaMiniClient.getTempUrl(accessToken, batchDownloadFile);
                        List<BatchDownloadFileResponse.FileList> resultList = response.getFileList();

                        // 替换原图片数组中的链接
                        for (int i = 0; i < resultList.size() && i < imageArray.size(); i++) {
                            if (resultList.get(i).getStatus() == 0) {
                                imageArray.set(i, resultList.get(i).getDownloadUrl());
                            }
                        }
                    }
                }
            }

            return skillObj.toJSONString();

        } catch (Exception e) {
            // 解析失败时保持原样
        }
        return equipmentPlan;
    }

    /**
     * 获取永久链接
     *
     * @param fileId 文件ID
     * @param accessToken 访问令牌
     * @return 永久链接
     */
    private String getPermanentUrl(String fileId, String accessToken) {
        try {
            // 准备调用云函数的参数
            Map<String, Object> params = new HashMap<>();
            params.put("fileID", fileId);

            // 调用云函数
            Map<String, Object> result = dotaMiniClient.callCloudFunction(accessToken, env, params);

            // 解析结果
            if (result != null && result.containsKey("resp_data")) {
                String respData = (String) result.get("resp_data");
                com.alibaba.fastjson.JSONObject jsonResult = com.alibaba.fastjson.JSON.parseObject(respData);

                if (jsonResult.getBooleanValue("success")) {
                    return jsonResult.getString("url");
                }
            }
        } catch (Exception e) {
            // 调用失败时记录日志
            log.error("获取永久链接失败: " + e.getMessage(), e);
        }

        return null;
    }

    /**
     * 批量获取永久链接
     *
     * @param fileIds 文件ID列表
     * @param accessToken 访问令牌
     * @return 永久链接列表
     */
    private List<String> getPermanentUrls(List<String> fileIds, String accessToken) {
        List<String> permanentUrls = new ArrayList<>();

        for (String fileId : fileIds) {
            String permanentUrl = getPermanentUrl(fileId, accessToken);
            permanentUrls.add(permanentUrl != null ? permanentUrl : fileId); // 如果获取失败，保留原始fileId
        }

        return permanentUrls;
    }

    /**
     * 处理通用图片列表，使用永久链接
     *
     * @param imgList
     * @param accessToken
     * @return
     */
    private String getGeneralPermanentUrl(String imgList, String accessToken) {
        if (imgList == null || imgList.isEmpty()) {
            return null;
        }
        try {
            com.alibaba.fastjson.JSONArray jsonArray = com.alibaba.fastjson.JSON.parseArray(imgList);
            List<String> fileIds = new ArrayList<>();

            // 收集所有文件ID
            for (int i = 0; i < jsonArray.size(); i++) {
                fileIds.add(jsonArray.getString(i));
            }

            // 批量获取永久链接
            List<String> permanentUrls = getPermanentUrls(fileIds, accessToken);

            // 构建返回的JSON数组
            com.alibaba.fastjson.JSONArray resultArray = new com.alibaba.fastjson.JSONArray();
            resultArray.addAll(permanentUrls);

            return resultArray.toJSONString();
        } catch (Exception e) {
            // 解析失败时保持原样
            log.error("处理图片链接失败: " + e.getMessage(), e);
        }
        return null;
    }

    /**
     * 处理装备计划图片，使用永久链接
     *
     * @param equipmentPlan
     * @param accessToken
     * @return
     */
    private String getEquipmentPlanPermanentUrl(String equipmentPlan, String accessToken) {
        if (equipmentPlan == null || equipmentPlan.isEmpty()) {
            return null;
        }
        try {
            com.alibaba.fastjson.JSONObject equipObj = com.alibaba.fastjson.JSON.parseObject(equipmentPlan);

            // 遍历每个装备分类
            for (String key : equipObj.keySet()) {
                com.alibaba.fastjson.JSONArray categoryArray = equipObj.getJSONArray(key);
                if (categoryArray != null && categoryArray.size() > 0) {
                    // 获取第一个元素（图片数组）
                    com.alibaba.fastjson.JSONArray imageArray = categoryArray.getJSONArray(0);
                    if (imageArray != null && imageArray.size() > 0) {
                        // 收集所有文件ID
                        List<String> fileIds = new ArrayList<>();
                        for (int i = 0; i < imageArray.size(); i++) {
                            fileIds.add(imageArray.getString(i));
                        }

                        // 批量获取永久链接
                        List<String> permanentUrls = getPermanentUrls(fileIds, accessToken);

                        // 替换原图片数组中的链接
                        for (int i = 0; i < permanentUrls.size() && i < imageArray.size(); i++) {
                            imageArray.set(i, permanentUrls.get(i));
                        }
                    }
                }
            }

            return equipObj.toJSONString();

        } catch (Exception e) {
            // 解析失败时保持原样
            log.error("处理装备计划图片链接失败: " + e.getMessage(), e);
        }
        return equipmentPlan;
    }

}

/**
 * // 处理技能计划
 * if (sysHero.getSkillPlan() != null) {
 * try {
 * String skillPlanStr = sysHero.getSkillPlan();
 * com.alibaba.fastjson.JSONArray skillArray = com.alibaba.fastjson.JSON.parseArray(skillPlanStr);
 * for (int i = 0; i < skillArray.size(); i++) {
 * String skillId = skillArray.getString(i);
 * skillArray.set(i, "cloud://cloud1-4gg6k9agc25e5eab.636c-cloud1-4gg6k9agc25e5eab-1257756543/dota1/img/j/" + skillId + ".jpg");
 * }
 * sysHero.setSkillPlan(skillArray.toJSONString());
 * } catch (Exception e) {
 * // 解析失败时保持原样
 * }
 * dotaMiniAppMapper.updateById(sysHero);
 * }
 * <p>
 * // 处理装备计划
 * if (sysHero.getEquipmentPlan() != null) {
 * try {
 * String equipPlanStr = sysHero.getEquipmentPlan();
 * com.alibaba.fastjson.JSONObject equipObj = com.alibaba.fastjson.JSON.parseObject(equipPlanStr);
 * <p>
 * for (String key : equipObj.keySet()) {
 * com.alibaba.fastjson.JSONArray equipArray = equipObj.getJSONArray(key);
 * if (equipArray != null && equipArray.size() > 0) {
 * com.alibaba.fastjson.JSONArray itemsArray = equipArray.getJSONArray(0);
 * if (itemsArray != null) {
 * for (int i = 0; i < itemsArray.size(); i++) {
 * String path = itemsArray.getString(i);
 * itemsArray.set(i, "cloud://cloud1-4gg6k9agc25e5eab.636c-cloud1-4gg6k9agc25e5eab-1257756543" + path);
 * }
 * }
 * }
 * }
 * <p>
 * sysHero.setEquipmentPlan(equipObj.toJSONString());
 * dotaMiniAppMapper.updateById(sysHero);
 * } catch (Exception e) {
 * // 解析失败时保持原样
 * }
 * }
 * <p>
 * // 处理协作英雄
 * if (sysHero.getCooperate() != null) {
 * try {
 * String cooperateStr = sysHero.getCooperate();
 * com.alibaba.fastjson.JSONArray cooperateArray = com.alibaba.fastjson.JSON.parseArray(cooperateStr);
 * <p>
 * // 获取英雄的attribute属性
 * String attributeFolder;
 * switch (sysHero.getAttribute()) {
 * case 0:
 * attributeFolder = "Power/";
 * break;
 * case 1:
 * attributeFolder = "Agile/";
 * break;
 * case 2:
 * attributeFolder = "Intelligence/";
 * break;
 * default:
 * attributeFolder = "";
 * }
 * <p>
 * // 构建基础路径
 * String basePath = "cloud://cloud1-4gg6k9agc25e5eab.636c-cloud1-4gg6k9agc25e5eab-1257756543/dota1/img/hero/"
 * + attributeFolder + sysHero.getCamp() + "/" + sysHero.getName() + "/";
 * <p>
 * // 处理每个协作英雄
 * for (int i = 0; i < cooperateArray.size(); i++) {
 * String heroName = cooperateArray.getString(i);
 * cooperateArray.set(i, basePath + heroName + ".gif");
 * }
 * <p>
 * sysHero.setCooperate(cooperateArray.toJSONString());
 * dotaMiniAppMapper.updateById(sysHero);
 * } catch (Exception e) {
 * // 解析失败时保持原样
 * }
 * }
 * <p>
 * if (sysHero.getRestraint() != null) {
 * try {
 * String restraint = sysHero.getRestraint();
 * com.alibaba.fastjson.JSONArray restraintArray = com.alibaba.fastjson.JSON.parseArray(restraint);
 * <p>
 * // 获取英雄的attribute属性
 * String attributeFolder;
 * switch (sysHero.getAttribute()) {
 * case 0:
 * attributeFolder = "Power/";
 * break;
 * case 1:
 * attributeFolder = "Agile/";
 * break;
 * case 2:
 * attributeFolder = "Intelligence/";
 * break;
 * default:
 * attributeFolder = "";
 * }
 * <p>
 * // 构建基础路径
 * String basePath = "cloud://cloud1-4gg6k9agc25e5eab.636c-cloud1-4gg6k9agc25e5eab-1257756543/dota1/img/hero/"
 * + attributeFolder + sysHero.getCamp() + "/" + sysHero.getName() + "/";
 * <p>
 * // 处理每个协作英雄
 * for (int i = 0; i < restraintArray.size(); i++) {
 * String heroName = restraintArray.getString(i);
 * restraintArray.set(i, basePath + heroName + ".gif");
 * }
 * <p>
 * sysHero.setRestraint(restraintArray.toJSONString());
 * dotaMiniAppMapper.updateById(sysHero);
 * } catch (Exception e) {
 * // 解析失败时保持原样
 * }
 * }
 * <p>
 * try {
 * String nickName = sysHero.getNickName();
 * <p>
 * // 获取英雄的attribute属性
 * String attributeFolder;
 * switch (sysHero.getAttribute()) {
 * case 0:
 * attributeFolder = "Power/";
 * break;
 * case 1:
 * attributeFolder = "Agile/";
 * break;
 * case 2:
 * attributeFolder = "Intelligence/";
 * break;
 * default:
 * attributeFolder = "";
 * }
 * <p>
 * // 构建基础路径
 * String basePath = "cloud://cloud1-4gg6k9agc25e5eab.636c-cloud1-4gg6k9agc25e5eab-1257756543/dota1/img/hero/"
 * + attributeFolder + sysHero.getCamp() + "/" + nickName + "/" + nickName + ".gif";
 * <p>
 * sysHero.setImg(basePath);
 * dotaMiniAppMapper.updateById(sysHero);
 * } catch (Exception e) {
 * // 解析失败时保持原样
 * }
 *         // 处理协作英雄
 * //        if (sysHero.getCooperate() != null) {
 * //            try {
 * //                String cooperateStr = sysHero.getCooperate();
 * //                com.alibaba.fastjson.JSONArray cooperateArray = com.alibaba.fastjson.JSON.parseArray(cooperateStr);
 * //                // 获取英雄的attribute属性
 * //                for (int i = 0; i < cooperateArray.size(); i++) {
 * //                    String heroName = cooperateArray.getString(i);
 * //                    String attributeFolder;
 * //                    switch (dotaMiniAppMapper.getAttributeByName(heroName)) {
 * //                        case 0:
 * //                            attributeFolder = "Power/";
 * //                            break;
 * //                        case 1:
 * //                            attributeFolder = "Agile/";
 * //                            break;
 * //                        case 2:
 * //                            attributeFolder = "Intelligence/";
 * //                            break;
 * //                        default:
 * //                            attributeFolder = "";
 * //                    }
 * //                    int campByNickName = dotaMiniAppMapper.getCampByNickName(heroName);
 * //                    String basePath = "cloud://cloud1-4gg6k9agc25e5eab.636c-cloud1-4gg6k9agc25e5eab-1257756543/dota1/img/hero/"
 * //                            + attributeFolder + campByNickName + "/";
 * //                    System.out.println("heroName = " + heroName);
 * //                    cooperateArray.set(i, basePath + heroName + "/" + heroName + ".gif");
 * //                }
 * //
 * //                sysHero.setCooperate(cooperateArray.toJSONString());
 * //                dotaMiniAppMapper.updateById(sysHero);
 * //            } catch (Exception e) {
 * //                // 解析失败时保持原样
 * //            }
 * //        }
 *
 *
 * //        if (sysHero.getRestraint() != null) {
 * //            try {
 * //                String restraintStr = sysHero.getRestraint();
 * //                com.alibaba.fastjson.JSONArray restraintArray = com.alibaba.fastjson.JSON.parseArray(restraintStr);
 * //                // 获取英雄的attribute属性
 * //                for (int i = 0; i < restraintArray.size(); i++) {
 * //                    String heroName = restraintArray.getString(i);
 * //                    String attributeFolder;
 * //                    switch (dotaMiniAppMapper.getAttributeByName(heroName)) {
 * //                        case 0:
 * //                            attributeFolder = "Power/";
 * //                            break;
 * //                        case 1:
 * //                            attributeFolder = "Agile/";
 * //                            break;
 * //                        case 2:
 * //                            attributeFolder = "Intelligence/";
 * //                            break;
 * //                        default:
 * //                            attributeFolder = "";
 * //                    }
 * //                    int campByNickName = dotaMiniAppMapper.getCampByNickName(heroName);
 * //                    String basePath = "cloud://cloud1-4gg6k9agc25e5eab.636c-cloud1-4gg6k9agc25e5eab-1257756543/dota1/img/hero/"
 * //                            + attributeFolder + campByNickName + "/";
 * //
 * //                    restraintArray.set(i, basePath + heroName + "/" + heroName + ".gif");
 * //                }
 * //
 * //                sysHero.setRestraint(restraintArray.toJSONString());
 * //                dotaMiniAppMapper.updateById(sysHero);
 * //            } catch (Exception e) {
 * //                // 解析失败时保持原样
 * //            }
 * //        }
 *
 *
 */
