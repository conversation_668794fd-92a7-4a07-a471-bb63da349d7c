# Technology Stack

## Core Framework
- **Spring Boot 2.5.5** with Java 11
- **<PERSON><PERSON>** for dependency management and build automation
- **MyBatis Plus 3.5.2** for database operations with MySQL 8.0.30

## Key Dependencies
- **Knife4j 3.0.3** - API documentation (Swagger)
- **Druid 1.2.15** - Database connection pooling
- **Redis** with Lettuce client for caching
- **Forest 1.5.28** - HTTP client for external API calls
- **WeChat Mini Program SDK 4.7.0** - WeChat integration
- **Fastjson 2.0.19** & Jackson - JSON processing
- **Hutool 5.8.10** - Java utility library
- **Lombok** - Code generation

## Common Commands

### Build & Run
```bash
# Clean and compile
./mvnw clean compile

# Run tests
./mvnw test

# Package application
./mvnw clean package

# Run application (dev profile)
./mvnw spring-boot:run

# Run with specific profile
./mvnw spring-boot:run -Dspring-boot.run.profiles=pro
```

### Database Operations
```bash
# Run database migrations (manual SQL execution required)
# Check existing tables first
mysql -u [user] -p [database] < check_existing_hero_table.sql

# Create new tables
mysql -u [user] -p [database] < create_hero_related_tables.sql
```

## Configuration Profiles
- `dev` - Development environment (default)
- `pro` - Production environment  
- `docker` - Docker deployment

## Port Configuration
- Default server port: **8201**