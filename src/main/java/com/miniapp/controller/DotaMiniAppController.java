package com.miniapp.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.common.enums.R;
import com.common.enums.ResultCodeEnum;
import com.miniapp.client.DotaMiniClient;
import com.miniapp.service.DotaMiniAppService;
import com.miniapp.service.HeroDataUpdateService;
import com.miniapp.service.HeroQueryService;
import com.model.SysHero;
import com.model.dto.HeroDetailDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Title: GoodsController
 * <AUTHOR>
 * @Package com.miniapp.controller
 * @Date 2022/12/5 14:21
 * @description: DOTA小程序控制器
 */
@Api(tags = "dotamini")
@RestController
@RequestMapping("miniapp/dota")
public class DotaMiniAppController {
    @Value("${token.env}")
    private String env;

    @Autowired
    private DotaMiniAppService dotaMiniAppService;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private DotaMiniClient dotaMiniClient;

    @Autowired
    private HeroDataUpdateService heroDataUpdateService;

    @Autowired
    private HeroQueryService heroQueryService;


    @ApiOperation("首页（新版本-从拆分表查询）")
    @PostMapping("/indexV2")
    public R indexV2(@RequestBody Map map) {
        String cacheKey = "dota:hero:list:v2:" + map.get("attribute") + ":" + map.get("camp");
        List<SysHero> list = (List<SysHero>) redisTemplate.opsForValue().get(cacheKey);
        if (list == null) {
            String attribute =  map.get("attribute").toString();
            String camp = map.get("camp").toString();
            list = heroQueryService.getHeroList(attribute, camp);
            // 注释掉图片永久链接处理 - 永久链接已存储在数据库中
            // list = getImgPermanent(list);
            redisTemplate.opsForValue().set(cacheKey, list);
        }
        return R.setResult(ResultCodeEnum.SUCCESS).data(list);
    }



    @ApiOperation("英雄详情（新版本-从拆分表查询）")
    @GetMapping("/heroDetailV2/{heroId}")
    public R heroDetailV2(@PathVariable("heroId") Integer heroId) {
        String cacheKey = "dota:hero:detail:v2:" + heroId;
        HeroDetailDTO heroDetail = (HeroDetailDTO) redisTemplate.opsForValue().get(cacheKey);
        if (heroDetail == null) {
            heroDetail = heroQueryService.getHeroDetail(heroId);
            if (heroDetail == null) {
                return R.setResult(ResultCodeEnum.FAIL).message("英雄不存在");
            }
            // 注释掉图片永久链接处理 - 永久链接已存储在数据库中
            // heroDetail = processHeroDetailImages(heroDetail);
            redisTemplate.opsForValue().set(cacheKey, heroDetail);
        }
        return R.setResult(ResultCodeEnum.SUCCESS).data(heroDetail);
    }


    @ApiOperation("首页")
    @PostMapping("/index")
    public R index(@RequestBody Map map) {
        String cacheKey = "dota:hero:list:" + map.get("attribute") + ":" + map.get("camp");
        List<SysHero> list = (List<SysHero>) redisTemplate.opsForValue().get(cacheKey);
        if (list == null) {
            QueryWrapper<SysHero> wrapper = new QueryWrapper<>();
            wrapper.eq("attribute", map.get("attribute"));
            wrapper.eq("camp", map.get("camp"));
            list = dotaMiniAppService.list(wrapper);
            // 改为使用永久链接
            list = getImgPermanent(list);
            redisTemplate.opsForValue().set(cacheKey, list);
        }
        return R.setResult(ResultCodeEnum.SUCCESS).data(list);
    }

    @ApiOperation("英雄详情")
    @GetMapping("/heroDetail/{heroId}")
    public R heroDetail(@PathVariable("heroId") Integer heroId) {
        String cacheKey = "dota:hero:detail:" + heroId;
        SysHero sysHero = (SysHero) redisTemplate.opsForValue().get(cacheKey);
        if (sysHero == null) {
            String accessToken = getAccessToken();
            sysHero = dotaMiniAppService.getDetailById(heroId, accessToken);
            redisTemplate.opsForValue().set(cacheKey, sysHero);
        }
        return R.setResult(ResultCodeEnum.SUCCESS).data(sysHero);
    }

    /**
     * 获取accessToken
     *
     * @return
     */
    private String getAccessToken() {
        Map<String, Object> map = dotaMiniClient.getAccessToken();
        return map.get("access_token").toString();
    }

    /**
     * 获取首页图片的永久下载链接
     *
     * @param list
     * @return
     */
    private List<SysHero> getImgPermanent(List<SysHero> list) {
        if (list == null || list.isEmpty()) {
            return list;
        }

        String accessToken = getAccessToken();

        // 收集所有需要处理的文件ID
        List<String> fileIds = new ArrayList<>();
        for (SysHero item : list) {
            if (item.getImg() != null && !item.getImg().isEmpty()) {
                fileIds.add(item.getImg());
            }
        }

        if (fileIds.isEmpty()) {
            return list;
        }

        // 批量获取永久链接
        Map<String, String> fileIdToUrlMap = new HashMap<>();
        for (String fileId : fileIds) {
            String permanentUrl = getPermanentUrl(fileId, accessToken);
            if (permanentUrl != null) {
                fileIdToUrlMap.put(fileId, permanentUrl);
            }
        }

        // 设置永久链接
        for (SysHero item : list) {
            if (item.getImg() != null && fileIdToUrlMap.containsKey(item.getImg())) {
                item.setImg(fileIdToUrlMap.get(item.getImg()));
            }
        }

        return list;
    }

    /**
     * 获取单个文件的永久链接
     *
     * @param fileId      文件ID
     * @param accessToken 访问令牌
     * @return 永久链接
     */
    private String getPermanentUrl(String fileId, String accessToken) {
        try {
            // 准备调用云函数的参数
            Map<String, Object> params = new HashMap<>();
            params.put("fileID", fileId);

            // 调用云函数
            Map<String, Object> result = dotaMiniClient.callCloudFunction(accessToken, env, params);

            // 解析结果
            if (result != null && result.containsKey("resp_data")) {
                String respData = (String) result.get("resp_data");
                com.alibaba.fastjson.JSONObject jsonResult = com.alibaba.fastjson.JSON.parseObject(respData);

                if (jsonResult.getBooleanValue("success")) {
                    return jsonResult.getString("url");
                }
            }
        } catch (Exception e) {
            // 调用失败时记录日志，但不影响程序运行
            System.err.println("获取永久链接失败: " + e.getMessage());
        }

        return null;
    }

    /**
     * 从Redis更新英雄数据到数据库
     *
     * @param heroId 英雄ID
     * @return 更新结果
     */
    @ApiOperation("从Redis更新英雄数据到数据库")
    @PostMapping("/updateHeroData/{heroId}")
    public R updateHeroData(@PathVariable("heroId") Integer heroId) {
        try {
            heroDataUpdateService.updateHeroDataFromRedis(heroId);
            return R.setResult(ResultCodeEnum.SUCCESS).message("英雄数据更新成功");
        } catch (Exception e) {
            return R.setResult(ResultCodeEnum.FAIL).message("英雄数据更新失败: " + e.getMessage());
        }
    }

}
