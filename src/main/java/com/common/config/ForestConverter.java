package com.common.config;

import com.alibaba.fastjson.serializer.SerializerFeature;
import com.dtflys.forest.converter.json.ForestFastjsonConverter;
import com.dtflys.forest.converter.json.ForestJsonConverter;
import org.springframework.context.annotation.Bean;

/**
 * @Title: ForestConverter
 * <AUTHOR>
 * @Package com.common.config
 * @Date 2022/12/27 10:30
 * @description: forest序列化转换器
 */
public class ForestConverter {
    @Bean
    public ForestJsonConverter forestFastjsonConverter() {
        ForestFastjsonConverter converter = new ForestFastjsonConverter();
        // 设置日期格式
        converter.setDateFormat("yyyy-MM-dd hh:mm:ss");
        // 设置序列化特性
        converter.setSerializerFeature(SerializerFeature.IgnoreErrorGetter);
        return converter;
    }

}
