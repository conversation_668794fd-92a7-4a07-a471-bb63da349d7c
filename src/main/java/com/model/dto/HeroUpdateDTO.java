package com.model.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 英雄更新DTO
 */
@Data
public class HeroUpdateDTO {
    
    private Integer id;
    private String name;
    private String nickName;
    private String img;
    private Integer attribute;
    private Integer camp;
    private String skillInfo;
    private String equipmentInfo;
    private String info;
    
    // 技能加点方案 - 按等级排序的图片列表
    private List<String> skillPlan;
    
    // 装备方案 - 按类型分组
    private Map<String, List<EquipmentUpdateItem>> equipmentPlan;
    
    // 配合英雄
    private List<HeroRelationUpdate> cooperate;
    
    // 克制英雄
    private List<HeroRelationUpdate> restraint;
    
    /**
     * 装备更新项
     */
    @Data
    public static class EquipmentUpdateItem {
        private String img;
        private Integer sortOrder;
        private String description;
    }
    
    /**
     * 英雄关系更新
     */
    @Data
    public static class HeroRelationUpdate {
        private String img;
        private String name;
    }
}