package com.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Predicate;

@Configuration
@EnableSwagger2
public class Knife4jConfig {

    @Bean
    public Docket docket(Environment environment) {
        // 添加接口请求头参数配置 没有的话 可以忽略
        ParameterBuilder tokenPar = new ParameterBuilder();
        List<Parameter> pars = new ArrayList<>();
        tokenPar.name("token")
                .description("令牌")
                .defaultValue("")
                .modelRef(new ModelRef("string"))
                .parameterType("header").required(false).build();
        pars.add(tokenPar.build());
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(this.apiInfo())
                //是否启动swagger 默认启动
                .enable(true)
                //所在分组
                .groupName("web")
                .select()
                //指定扫描的包路径
                .apis(RequestHandlerSelectors.basePackage("com.miniapp.controller"))
                // 不显示错误的接口
                .paths(Predicate.not(PathSelectors.regex("/error.*"))) // 错误路径不监控
                .paths(PathSelectors.regex("/.*")) // 对跟下所有路径进行监控
                .build()
                .globalOperationParameters(pars);
    }

    private ApiInfo apiInfo() {
        Contact author = new Contact("Edyx", "", "");
        return new ApiInfo(
                "MiniApp文档",
                "MiniApp文档",
                "1.0",
                "",
                author,
                "",
                "",
                new ArrayList()
        );

    }

}
