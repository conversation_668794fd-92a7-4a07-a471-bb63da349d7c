<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.miniapp.mapper.DotaMiniAppMapper">

    <select id="getAttributeByName" resultType="int">
        SELECT attribute FROM sys_hero WHERE nick_name = #{name}
    </select>

    <select id="getCampByNickName" resultType="int">
        SELECT camp FROM sys_hero WHERE nick_name = #{nickName}
    </select>
</mapper>
