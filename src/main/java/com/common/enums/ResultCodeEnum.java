package com.common.enums;

import lombok.Getter;

@Getter
public enum ResultCodeEnum {

    /**
     * 操作成功
     **/
    SUCCESS(true,200, "操作成功"),
    /**
     * 非法访问
     **/
    UNAUTHORIZED(false,401, "非法访问"),
    /**
     * 没有权限
     **/
    NOT_PERMISSION(false,403, "没有权限"),
    /**
     * 你请求的资源不存在
     **/
    NOT_FOUND(false,404, "你请求的资源不存在"),
    /**
     * 操作失败
     **/
    FAIL(false,500, "操作失败"),
    /**
     * 登录失败
     **/
    LOGIN_EXCEPTION(false,400, "登录失败"),
    /**
     * 系统异常
     **/
    SYSTEM_EXCEPTION(false,5000, "系统异常"),
    /**
     * 请求参数校验异常
     **/
    PARAMETER_EXCEPTION(false,5001, "请求参数校验异常"),
    /**
     * 请求参数解析异常
     **/
    PARAMETER_PARSE_EXCEPTION(false,5002, "请求参数解析异常"),
    /**
     * HTTP内容类型异常
     **/
    HTTP_MEDIA_TYPE_EXCEPTION(false,5003, "HTTP内容类型异常"),
    /**
     * 系统处理异常
     **/
    SPRING_BOOT_PLUS_EXCEPTION(false,5100, "系统处理异常"),
    /**
     * 业务处理异常
     **/
    BUSINESS_EXCEPTION(false,5101, "业务处理异常"),
    /**
     * 数据库处理异常
     **/
    DAO_EXCEPTION(false,5102, "数据库处理异常"),
    /**
     * 验证码校验异常
     **/
    VERIFICATION_CODE_EXCEPTION(false,5103, "验证码校验异常"),
    /**
     * 登录授权异常
     **/
    AUTHENTICATION_EXCEPTION(false,5104, "登录授权异常"),
    /**
     * 没有访问权限
     **/
    UNAUTHENTICATED_EXCEPTION(false,5105, "没有访问权限"),
    /**
     * 没有访问权限
     **/
    UNAUTHORIZED_EXCEPTION(false,5106, "没有访问权限"),
    /**
     * JWT Token解析异常
     **/
    JWTDECODE_EXCEPTION(false,5107, "Token解析异常"),

    /**
     * 方法不支持异常
     **/
    HTTP_REQUEST_METHOD_NOT_SUPPORTED_EXCEPTION(false,5108, "METHOD NOT SUPPORTED"),

    /**
     * 空指针异常
     **/
    NULL_POINT(false,5109, "空指针异常"),

    /**
     * HTTP客户端异常
     **/
    HTTP_CLIENT_ERROR(false,5110, "HTTP客户端异常"),

    /**
     * 短信发送失败
     **/
    MSG_ERROR(false,6301, "查询失败"),

    /**
     *  校验失败
     **/
    VERIFY_CODE_ERROR(false,8002, "校验失败"),

    /**
     *  订单查询失败
     **/
    ORDER_QUERY_ERROR(false,2222, "应答失败"),

    /**
     * 号码不存在
     */
    NULL_NUMBER(false, 8888, "号码不存在"),

    /**
     * 其他错误
     */
    OTHER_ERROR(false, 9999, "其他错误"),

    /**
     * 没有订单信息
     */
    NO_ORDER(false,2017,"没有查询到订单信息"),

    /**
     * 订单编号为空
     */
    NULL_ORDERID(false,100009,"订单号为空")
    ;

    // 响应是否成功
    private Boolean success;
    // 响应状态码
    private Integer code;
    // 响应信息
    private String message;

    ResultCodeEnum(boolean success, Integer code, String message) {
        this.success = success;
        this.code = code;
        this.message = message;
    }
}