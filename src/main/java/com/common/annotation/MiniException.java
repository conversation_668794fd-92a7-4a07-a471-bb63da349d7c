package com.common.annotation;

import com.common.enums.ResultCodeEnum;
import lombok.Data;

/**
 * @Title: MiniException
 * <AUTHOR>
 * @Package com.miniapp.exception
 * @Date 2022/11/28 18:57
 * @description: 自定义异常
 */
@Data
public class MiniException extends RuntimeException {
    private Integer code;

    public MiniException(Integer code, String message) {
        super(message);
        this.code = code;
    }

    public MiniException(ResultCodeEnum resultCodeEnum) {
        super(resultCodeEnum.getMessage());
        this.code = resultCodeEnum.getCode();
    }

    @Override
    public String toString() {
        return "MiniException{" + "code=" + code + ", message=" + this.getMessage() + '}';
    }


}
