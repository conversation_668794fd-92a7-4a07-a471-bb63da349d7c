package com.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 英雄克制关系实体类
 */
@Data
@TableName("sys_hero_restraint")
public class SysHeroRestraint {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    private Integer heroId;
    
    private String restraintHeroImg;
    
    private String restraintHeroName;
    
    private LocalDateTime createTime;
}