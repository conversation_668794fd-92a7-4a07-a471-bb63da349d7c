package com.miniapp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.miniapp.service.*;
import com.model.*;
import com.model.dto.HeroDetailDTO;
import com.model.dto.HeroUpdateDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 英雄管理服务实现类
 */
@Service
public class HeroManageServiceImpl implements HeroManageService {

    @Autowired
    private SysHeroService sysHeroService;
    
    @Autowired
    private SysHeroSkillPlanService sysHeroSkillPlanService;
    
    @Autowired
    private SysHeroEquipmentPlanService sysHeroEquipmentPlanService;
    
    @Autowired
    private SysHeroCooperateService sysHeroCooperateService;
    
    @Autowired
    private SysHeroRestraintService sysHeroRestraintService;
    
    @Autowired
    private HeroQueryService heroQueryService;

    @Override
    public List<HeroDetailDTO> getAllHeroesWithDetails() {
        List<SysHero> heroes = sysHeroService.list();
        List<HeroDetailDTO> result = new ArrayList<>();
        
        for (SysHero hero : heroes) {
            HeroDetailDTO detail = heroQueryService.getHeroDetail(hero.getId());
            if (detail != null) {
                result.add(detail);
            }
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateHeroDetails(HeroUpdateDTO updateDTO) {
        try {
            Integer heroId = updateDTO.getId();
            
            // 1. 更新英雄基础信息
            SysHero hero = sysHeroService.getById(heroId);
            if (hero == null) {
                return false;
            }
            
            hero.setName(updateDTO.getName());
            hero.setNickName(updateDTO.getNickName());
            hero.setImg(updateDTO.getImg());
            hero.setAttribute(updateDTO.getAttribute());
            hero.setCamp(updateDTO.getCamp());
            hero.setSkillInfo(updateDTO.getSkillInfo());
            hero.setEquipmentInfo(updateDTO.getEquipmentInfo());
            hero.setInfo(updateDTO.getInfo());
            
            sysHeroService.updateById(hero);
            
            // 2. 更新技能加点方案
            if (updateDTO.getSkillPlan() != null) {
                updateSkillPlan(heroId, updateDTO.getSkillPlan());
            }
            
            // 3. 更新装备方案
            if (updateDTO.getEquipmentPlan() != null) {
                updateEquipmentPlan(heroId, updateDTO.getEquipmentPlan());
            }
            
            // 4. 更新配合英雄
            if (updateDTO.getCooperate() != null) {
                updateCooperateHeroes(heroId, updateDTO.getCooperate());
            }
            
            // 5. 更新克制英雄
            if (updateDTO.getRestraint() != null) {
                updateRestraintHeroes(heroId, updateDTO.getRestraint());
            }
            
            return true;
        } catch (Exception e) {
            throw new RuntimeException("更新英雄信息失败: " + e.getMessage(), e);
        }
    }
    
    private void updateSkillPlan(Integer heroId, List<String> skillPlan) {
        // 删除原有技能方案
        QueryWrapper<SysHeroSkillPlan> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.eq("hero_id", heroId);
        sysHeroSkillPlanService.remove(deleteWrapper);
        
        // 插入新的技能方案
        for (int i = 0; i < skillPlan.size(); i++) {
            SysHeroSkillPlan skillPlanEntity = new SysHeroSkillPlan();
            skillPlanEntity.setHeroId(heroId);
            skillPlanEntity.setLevel(i + 1);
            skillPlanEntity.setSkillImg(skillPlan.get(i));
            sysHeroSkillPlanService.save(skillPlanEntity);
        }
    }
    
    private void updateEquipmentPlan(Integer heroId, Map<String, List<HeroUpdateDTO.EquipmentUpdateItem>> equipmentPlan) {
        // 删除原有装备方案
        QueryWrapper<SysHeroEquipmentPlan> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.eq("hero_id", heroId);
        sysHeroEquipmentPlanService.remove(deleteWrapper);
        
        // 插入新的装备方案
        for (Map.Entry<String, List<HeroUpdateDTO.EquipmentUpdateItem>> entry : equipmentPlan.entrySet()) {
            String planType = entry.getKey();
            List<HeroUpdateDTO.EquipmentUpdateItem> items = entry.getValue();
            
            for (int i = 0; i < items.size(); i++) {
                HeroUpdateDTO.EquipmentUpdateItem item = items.get(i);
                SysHeroEquipmentPlan equipmentPlanEntity = new SysHeroEquipmentPlan();
                equipmentPlanEntity.setHeroId(heroId);
                equipmentPlanEntity.setPlanType(planType);
                equipmentPlanEntity.setEquipmentImg(item.getImg());
                equipmentPlanEntity.setSortOrder(item.getSortOrder() != null ? item.getSortOrder() : i + 1);
                equipmentPlanEntity.setDescription(item.getDescription());
                sysHeroEquipmentPlanService.save(equipmentPlanEntity);
            }
        }
    }
    
    private void updateCooperateHeroes(Integer heroId, List<HeroUpdateDTO.HeroRelationUpdate> cooperate) {
        // 删除原有配合关系
        QueryWrapper<SysHeroCooperate> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.eq("hero_id", heroId);
        sysHeroCooperateService.remove(deleteWrapper);
        
        // 插入新的配合关系
        for (HeroUpdateDTO.HeroRelationUpdate relation : cooperate) {
            SysHeroCooperate cooperateEntity = new SysHeroCooperate();
            cooperateEntity.setHeroId(heroId);
            cooperateEntity.setCooperateHeroImg(relation.getImg());
            cooperateEntity.setCooperateHeroName(relation.getName());
            sysHeroCooperateService.save(cooperateEntity);
        }
    }
    
    private void updateRestraintHeroes(Integer heroId, List<HeroUpdateDTO.HeroRelationUpdate> restraint) {
        // 删除原有克制关系
        QueryWrapper<SysHeroRestraint> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.eq("hero_id", heroId);
        sysHeroRestraintService.remove(deleteWrapper);
        
        // 插入新的克制关系
        for (HeroUpdateDTO.HeroRelationUpdate relation : restraint) {
            SysHeroRestraint restraintEntity = new SysHeroRestraint();
            restraintEntity.setHeroId(heroId);
            restraintEntity.setRestraintHeroImg(relation.getImg());
            restraintEntity.setRestraintHeroName(relation.getName());
            sysHeroRestraintService.save(restraintEntity);
        }
    }
}