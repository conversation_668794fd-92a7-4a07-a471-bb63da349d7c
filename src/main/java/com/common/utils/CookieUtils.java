package com.common.utils;

import java.net.HttpCookie;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CookieUtils {

    // 模拟原JS中的环境检测
    public static boolean isStandardBrowserEnv() {
        // 实际应用中可检查User-Agent等
        return true;
    }

    // Cookie操作接口
    public interface CookieOperations {
        String write(String name, String value, Long expires, String path, String domain, Boolean secure);
        String read(String name, String cookieString);
        String remove(String name);
    }

    // 标准浏览器环境实现
    private static class StandardCookieOperations implements CookieOperations {
        @Override
        public String write(String name, String value, Long expires, String path, String domain, Boolean secure) {
            HttpCookie cookie = new HttpCookie(name, value);

            if (expires != null) {
                cookie.setMaxAge((expires - System.currentTimeMillis()) / 1000);
            }
            if (path != null) {
                cookie.setPath(path);
            }
            if (domain != null) {
                cookie.setDomain(domain);
            }
            if (secure != null && secure) {
                cookie.setSecure(true);
            }

            return cookie.toString();
        }

        @Override
        public String read(String name, String cookieString) {
            Pattern pattern = Pattern.compile("(?:^|;\\s*)" + name + "=([^;]*)");
            Matcher matcher = pattern.matcher(cookieString);
            return matcher.find() ? matcher.group(1) : null;
        }

        @Override
        public String remove(String name) {
            HttpCookie cookie = new HttpCookie(name, "");
            cookie.setMaxAge(0);
            return cookie.toString();
        }
    }

    // 非浏览器环境空实现
    private static class DummyCookieOperations implements CookieOperations {
        @Override
        public String write(String name, String value, Long expires, String path, String domain, Boolean secure) {
            return null;
        }

        @Override
        public String read(String name, String cookieString) {
            return null;
        }

        @Override
        public String remove(String name) {
            return null;
        }
    }

    // 获取Cookie操作实例
    public static CookieOperations getCookieOperations() {
        return isStandardBrowserEnv() ? new StandardCookieOperations() : new DummyCookieOperations();
    }

    // 使用示例
    public static void main(String[] args) {
        CookieOperations cookieOps = getCookieOperations();

        // 写入Cookie
        String cookieStr = cookieOps.write(
            "test",
            "hello",
            System.currentTimeMillis() + 3600000, // 1小时后过期
            "/",
            "example.com",
            true
        );
        System.out.println("Set-Cookie: " + cookieStr);

        // 读取Cookie
        String cookieValue = cookieOps.read("test", "test=hello; other=value");
        System.out.println("Cookie value: " + cookieValue);

        // 删除Cookie
        String removeStr = cookieOps.remove("test");
        System.out.println("Remove-Cookie: " + removeStr);
    }
}
