# Project Structure

## Package Organization
The project follows a layered architecture with clear separation of concerns:

```
src/main/java/com/
├── common/           # Shared utilities and configurations
│   ├── annotation/   # Custom annotations and exception handlers
│   ├── aop/         # Aspect-oriented programming (request/response advice)
│   ├── config/      # Configuration classes (Redis, MyBatis, Knife4j, etc.)
│   ├── enums/       # Response enums and result codes
│   └── utils/       # Utility classes (JSON, HTTP, Token, Signature)
├── miniapp/         # Main application package
│   ├── client/      # External API clients (WeChat, Forest HTTP)
│   ├── controller/  # REST API endpoints
│   ├── mapper/      # MyBatis data access layer
│   └── service/     # Business logic layer
│       └── impl/    # Service implementations
└── model/           # Entity classes and data models
```

## Key Architectural Patterns

### Layered Architecture
- **Controller** → **Service** → **Mapper** → **Database**
- Clear separation between web, business, and data access layers

### Configuration Management
- Environment-specific configs in `src/main/resources/application-{profile}.yml`
- Centralized configuration classes in `com.common.config`

### Exception Handling
- Global exception handler in `com.common.annotation.GlobalExceptionHandler`
- Custom exceptions: `MiniException`, `MyException`

### AOP Integration
- Request/response body advice for logging and processing
- Signature processing for security

## Database Design
- **Primary Entity**: `sys_hero` (main hero table)
- **Related Tables**: 
  - `sys_hero_skill_plan` - Hero skill upgrade plans
  - `sys_hero_equipment_plan` - Equipment builds
  - `sys_hero_cooperate` - Hero synergies
  - `sys_hero_restraint` - Hero counters

## Naming Conventions
- **Packages**: lowercase with dots (com.miniapp.service)
- **Classes**: PascalCase (DotaMiniAppController)
- **Methods**: camelCase (updateHeroData)
- **Database**: snake_case (sys_hero, hero_id)
- **API Endpoints**: kebab-case paths (/miniapp/dota/hero-detail)

## Resource Organization
- **Mappers**: XML files in `src/main/resources/mapper/`
- **Configs**: YAML files in `src/main/resources/`
- **Logging**: Logback configuration in `logback-spring.xml`