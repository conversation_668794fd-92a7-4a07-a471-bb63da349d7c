package com.miniapp.controller;

import com.common.enums.R;
import com.common.enums.ResultCodeEnum;
import com.miniapp.service.HeroManageService;
import com.miniapp.service.HeroQueryService;
import com.model.dto.HeroDetailDTO;
import com.model.dto.HeroUpdateDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * @Title: DotaMiniManageController
 * <AUTHOR>
 * @Package com.miniapp.controller
 * @Date 2025/8/8 09:34
 * @description: DOTA英雄管理控制器
 */
@Api(tags = "DOTA英雄管理")
@Controller
@RequestMapping("/manage/dota")
public class DotaMiniManageController {

    @Autowired
    private HeroQueryService heroQueryService;

    @Autowired
    private HeroManageService heroManageService;

    /**
     * 英雄管理首页 - 直接返回HTML内容
     */
    @GetMapping("/")
    @ResponseBody
    public String index(HttpServletResponse response) throws IOException {
        response.setContentType("text/html;charset=UTF-8");

        // 读取静态HTML文件内容
        try {
            java.io.InputStream inputStream = getClass().getClassLoader()
                    .getResourceAsStream("static/hero-manage.html");
            if (inputStream != null) {
                java.io.BufferedReader reader = new java.io.BufferedReader(
                        new java.io.InputStreamReader(inputStream, java.nio.charset.StandardCharsets.UTF_8));
                StringBuilder content = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    content.append(line).append("\n");
                }
                reader.close();
                return content.toString();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 如果读取失败，尝试读取英文版本
        try {
            java.io.InputStream inputStream2 = getClass().getClassLoader()
                    .getResourceAsStream("static/hero-manage-en.html");
            if (inputStream2 != null) {
                java.io.BufferedReader reader = new java.io.BufferedReader(
                        new java.io.InputStreamReader(inputStream2, java.nio.charset.StandardCharsets.UTF_8));
                StringBuilder content = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    content.append(line).append("\n");
                }
                reader.close();
                return content.toString();
            }
        } catch (Exception e2) {
            e2.printStackTrace();
        }

        // 如果都失败，返回错误页面
        return "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>Error</title></head><body>" +
                "<h1>Page Load Failed</h1>" +
                "<p>Unable to load hero management page, please check server configuration.</p>" +
                "<p><a href='/manage/dota/test'>Test Backend Service</a></p>" +
                "</body></html>";
    }

    /**
     * 测试页面访问
     */
    @GetMapping("/test")
    @ResponseBody
    public String test() {
        return "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>Test Page</title></head><body>" +
                "<h1>DOTA Hero Management System - Backend Service Running!</h1>" +
                "<p>System Check:</p>" +
                "<ul>" +
                "<li><a href='/manage/dota/api/status'>System Status Check</a></li>" +
                "<li><a href='/manage/dota/api/encoding-test'>Encoding Test</a></li>" +
                "<li><a href='/manage/dota/default-avatar.png'>Default Avatar Test</a></li>" +
                "</ul>" +
                "<p>API Interface Test:</p>" +
                "<ul>" +
                "<li><a href='/manage/dota/api/heroes'>Get Heroes List</a></li>" +
                "<li><a href='/manage/dota/api/hero/49'>Get Hero Detail (ID:49)</a></li>" +
                "</ul>" +
                "<p><a href='/manage/dota/'>Back to Management Page (Chinese)</a></p>" +
                "<p><a href='/manage/dota/en'>English Management Page</a></p>" +
                "</body></html>";
    }

    /**
     * 提供默认头像
     */
    @GetMapping("/default-avatar.png")
    public void defaultAvatar(HttpServletResponse response) throws IOException {
        response.setContentType("image/svg+xml");
        response.setCharacterEncoding("UTF-8");

        String svg = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
                "<svg width=\"50\" height=\"50\" viewBox=\"0 0 50 50\" xmlns=\"http://www.w3.org/2000/svg\">" +
                "<rect width=\"50\" height=\"50\" fill=\"#e0e0e0\"/>" +
                "<circle cx=\"25\" cy=\"20\" r=\"8\" fill=\"#999\"/>" +
                "<path d=\"M10 40 Q25 30 40 40 L40 50 L10 50 Z\" fill=\"#999\"/>" +
                "<text x=\"25\" y=\"45\" text-anchor=\"middle\" font-size=\"8\" fill=\"#666\">HERO</text>" +
                "</svg>";

        response.getWriter().write(svg);
    }

    /**
     * 检查系统状态
     */
    @GetMapping("/api/status")
    @ResponseBody
    public R checkStatus() {
        try {
            // 检查基础服务
            java.util.Map<String, Object> status = new java.util.HashMap<>();
            status.put("server", "运行正常");
            status.put("timestamp", new java.util.Date());

            // 尝试检查数据库连接
            try {
                // 先检查基础的Hero服务
                if (heroQueryService != null) {
                    status.put("heroQueryService", "已注入");
                } else {
                    status.put("heroQueryService", "未注入");
                }

                if (heroManageService != null) {
                    status.put("heroManageService", "已注入");
                } else {
                    status.put("heroManageService", "未注入");
                }

                List<HeroDetailDTO> heroes = heroManageService.getAllHeroesWithDetails();
                status.put("database", "连接正常");
                status.put("heroCount", heroes != null ? heroes.size() : 0);

                // 如果有英雄数据，显示第一个英雄的信息作为示例
                if (heroes != null && !heroes.isEmpty()) {
                    HeroDetailDTO firstHero = heroes.get(0);
                    status.put("sampleHero", firstHero.getName() + " (ID: " + firstHero.getId() + ")");
                }

            } catch (Exception e) {
                status.put("database", "连接异常: " + e.getMessage());
                status.put("heroCount", 0);
                status.put("error", e.getClass().getSimpleName());
                e.printStackTrace(); // 打印详细错误信息
            }

            return R.setResult(ResultCodeEnum.SUCCESS).data(status);
        } catch (Exception e) {
            return R.setResult(ResultCodeEnum.FAIL).message("状态检查失败: " + e.getMessage());
        }
    }

    /**
     * 编码测试接口
     */
    @GetMapping("/api/encoding-test")
    public void encodingTest(HttpServletResponse response) throws IOException {
        response.setContentType("text/html;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");

        // 使用字节数组确保中文正确编码
        String html = "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>Encoding Test</title></head><body>" +
                "<h1>Encoding Test Results</h1>" +
                "<p>Default Charset: " + java.nio.charset.Charset.defaultCharset().toString() + "</p>" +
                "<p>File Encoding: " + System.getProperty("file.encoding") + "</p>" +
                "<p>User Language: " + System.getProperty("user.language") + "</p>" +
                "<p>User Country: " + System.getProperty("user.country") + "</p>" +
                "<hr>" +
                "<p>Chinese Test: 中文测试英雄管理系统</p>" +
                "<p>Special Characters: ①②③④⑤</p>" +
                "<p>Hero Names: 水晶室女、魅惑魔女、仙女龙</p>" +
                "</body></html>";

        // 使用UTF-8字节数组写入
        byte[] bytes = html.getBytes(java.nio.charset.StandardCharsets.UTF_8);
        response.getOutputStream().write(bytes);
    }

    /**
     * English version of hero management page
     */
    @GetMapping("/en")
    @ResponseBody
    public String indexEn(HttpServletResponse response) throws IOException {
        response.setContentType("text/html;charset=UTF-8");

        // Read English version HTML file
        try {
            java.io.InputStream inputStream = getClass().getClassLoader()
                    .getResourceAsStream("static/hero-manage-en.html");
            if (inputStream != null) {
                java.io.BufferedReader reader = new java.io.BufferedReader(
                        new java.io.InputStreamReader(inputStream, java.nio.charset.StandardCharsets.UTF_8));
                StringBuilder content = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    content.append(line).append("\n");
                }
                reader.close();
                return content.toString();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        // If failed, return error page
        return "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>Error</title></head><body>" +
                "<h1>Page Load Failed</h1>" +
                "<p>Unable to load English hero management page.</p>" +
                "<p><a href='/manage/dota/test'>Test Backend Service</a></p>" +
                "</body></html>";
    }

    /**
     * 获取英雄列表（API接口）
     */
    @ApiOperation("获取英雄列表")
    @GetMapping("/api/heroes")
    @ResponseBody
    public R getHeroesList() {
        try {
            System.out.println("开始获取英雄列表...");
            List<HeroDetailDTO> heroes = heroManageService.getAllHeroesWithDetails();
            System.out.println("获取到英雄数量: " + (heroes != null ? heroes.size() : 0));

            if (heroes == null || heroes.isEmpty()) {
                return R.setResult(ResultCodeEnum.SUCCESS).data(new java.util.ArrayList<>()).message("暂无英雄数据");
            }

            return R.setResult(ResultCodeEnum.SUCCESS).data(heroes);
        } catch (Exception e) {
            System.err.println("获取英雄列表失败: " + e.getMessage());
            e.printStackTrace();
            return R.setResult(ResultCodeEnum.FAIL).message("获取英雄列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取单个英雄详情（API接口）
     */
    @ApiOperation("获取英雄详情")
    @GetMapping("/api/hero/{heroId}")
    @ResponseBody
    public R getHeroDetail(@PathVariable("heroId") Integer heroId) {
        try {
            HeroDetailDTO hero = heroQueryService.getHeroDetail(heroId);
            if (hero == null) {
                return R.setResult(ResultCodeEnum.FAIL).message("英雄不存在");
            }
            return R.setResult(ResultCodeEnum.SUCCESS).data(hero);
        } catch (Exception e) {
            return R.setResult(ResultCodeEnum.FAIL).message("获取英雄详情失败: " + e.getMessage());
        }
    }

    /**
     * 更新英雄信息（API接口）
     */
    @ApiOperation("更新英雄信息")
    @PostMapping("/api/hero/{heroId}")
    @ResponseBody
    public R updateHero(@PathVariable("heroId") Integer heroId, @RequestBody HeroUpdateDTO updateDTO) {
        try {
            updateDTO.setId(heroId);
            boolean success = heroManageService.updateHeroDetails(updateDTO);
            if (success) {
                return R.setResult(ResultCodeEnum.SUCCESS).message("英雄信息更新成功");
            } else {
                return R.setResult(ResultCodeEnum.FAIL).message("英雄信息更新失败");
            }
        } catch (Exception e) {
            return R.setResult(ResultCodeEnum.FAIL).message("更新英雄信息失败: " + e.getMessage());
        }
    }
}
