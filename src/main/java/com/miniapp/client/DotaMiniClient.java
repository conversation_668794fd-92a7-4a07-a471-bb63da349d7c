package com.miniapp.client;

import com.dtflys.forest.annotation.Get;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.annotation.Var;
import com.model.BatchDownloadFile;
import com.model.BatchDownloadFileResponse;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Title: client
 * <AUTHOR>
 * @Package com.miniapp
 * @Date 2022/12/8 14:49
 * @description: 统一HTTP请求客户端接口
 */
@Component
public interface DotaMiniClient {
    String basePath = "https://api.weixin.qq.com/cgi-bin/token";
    String tempUrl = "https://api.weixin.qq.com/tcb/batchdownloadfile?access_token=";
    String appId = "wx57a6173687c26c64";
    String appSecret = "20a8debd167fe61c94767f3d333f542a";
    String env = "cloud1-4gg6k9agc25e5eab";

    @Get(url = basePath, data = {
            "grant_type=client_credential",
            "appid=" + appId,
            "secret=" + appSecret
    })
    Map<String,Object> getAccessToken();


    @Post(url = tempUrl+"{accessToken}")
    BatchDownloadFileResponse getTempUrl(@Var("accessToken") String accessToken, @JSONBody BatchDownloadFile batchDownloadFile);

    // 新增调用云函数的接口方法
    @Post(url = "https://api.weixin.qq.com/tcb/invokecloudfunction?access_token={accessToken}&env={env}&name=getPermanentURL")
    Map<String, Object> callCloudFunction(@Var("accessToken") String accessToken, @Var("env") String env, @JSONBody Map<String, Object> params);

}
