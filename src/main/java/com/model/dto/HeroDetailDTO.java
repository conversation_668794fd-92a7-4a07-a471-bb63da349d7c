package com.model.dto;

import com.model.SysHero;
import com.model.SysHeroSkillPlan;
import com.model.SysHeroEquipmentPlan;
import com.model.SysHeroCooperate;
import com.model.SysHeroRestraint;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 英雄详情DTO - 组合拆分后的数据
 */
@Data
public class HeroDetailDTO {
    
    // 基础信息
    private Integer id;
    private String name;
    private String nickName;
    private String img;
    private Integer attribute;
    private Integer camp;
    private String skillInfo;
    private String equipmentInfo;
    private String info;
    
    // 技能加点方案 - 按等级排序的图片列表
    private List<String> skillPlan;
    
    // 装备方案 - 按类型分组
    private Map<String, List<EquipmentItem>> equipmentPlan;
    
    // 配合英雄
    private List<HeroRelation> cooperate;
    
    // 克制英雄
    private List<HeroRelation> restraint;
    
    /**
     * 装备项
     */
    @Data
    public static class EquipmentItem {
        private String img;
        private Integer sortOrder;
        private String description;
    }
    
    /**
     * 英雄关系
     */
    @Data
    public static class HeroRelation {
        private String img;
        private String name;
    }
}