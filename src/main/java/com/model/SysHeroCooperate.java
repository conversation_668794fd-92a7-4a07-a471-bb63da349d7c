package com.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 英雄配合关系实体类
 */
@Data
@TableName("sys_hero_cooperate")
public class SysHeroCooperate {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    private Integer heroId;
    
    private String cooperateHeroImg;
    
    private String cooperateHeroName;
    
    private LocalDateTime createTime;
}