spring:
  application:
    name: dev
  datasource:
    username: root
    password: 7KTYj5sJ
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************************************************************************
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      #初始化连接池大小
      initial-size: 5
      #配置最小连接数
      min-idle: 5
      #配置最大连接数
      max-active: 200
      #配置连接等待超时时间
      max-wait: 60000
      #配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      #配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      #测试连接
      validation-query: SELECT 1 FROM DUAL
      #申请连接的时候检测，建议配置为true，不影响性能，并且保证安全
      test-while-idle: true
      #获取连接时执行检测，建议关闭，影响性能
      test-on-borrow: false
      #归还连接时执行检测，建议关闭，影响性能
      test-on-return: false
      #是否开启PSCache，PSCache对支持游标的数据库性能提升巨大，oracle建议开启，mysql下建议关闭
      pool-prepared-statements: false
      #开启poolPreparedStatements后生效
      max-pool-prepared-statement-per-connection-size: 20
      #配置扩展插件，常用的插件有=>stat:监控统计  log4j:日志  wall:防御sql注入
      filters: stat,wall,slf4j
      #打开mergeSql功能；慢SQL记录
      connection-properties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000

  redis:
#    host: 127.0.0.1 # Redis服务器地址 主
    host: redis-15704.crce178.ap-east-1-1.ec2.redns.redis-cloud.com # Redis服务器地址 主
    database: 0 # Redis数据库索引（默认为0）
#    port: 6379 # Redis服务器连接端口
    port: 15704 # Redis服务器连接端口
    password: SOXU2OovNLw35eHay8IdjJF53CJhFEdb # Redis服务器连接密码（默认为空）
    # https://support.huaweicloud.com/usermanual-dcs/dcs-ug-0312024.html
    timeout: 5000ms # 连接超时时间（毫秒）参数设为0表示连接永不断开。
    lettuce:
      pool:
        max-active: 8 # 连接池最大连接数
        max-idle: 8 # 连接池最大空闲连接数
        min-idle: 0 # 连接池最小空闲连接数
        max-wait: -1ms # 连接池最大阻塞等待时间，负值表示没有限制
#    sentinel:
#      master: jfscredis
#      nodes: *************:32510,*************:32513,*************:32512
  cache:
    redis:
      cache-null-values: false

# 注意：添加MD5密钥
signKey: 1234567890abcdef

# 注意：添加微信小程序的appid和appsecret
token:
  appId: wx57a6173687c26c64
  appSecret: 20a8debd167fe61c94767f3d333f542a
  env: cloud1-4gg6k9agc25e5eab

wo:

  appSecret: FB6A5A1FFAA61597
  basePath: http://api.10155.com/woplus/orderFreeProduct
