package com.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Title: BatchDownloadFileResponse
 * <AUTHOR>
 * @Package com.model
 * @Date 2025/7/17 12:45
 * @description:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class BatchDownloadFileResponse implements Serializable {

    @JsonProperty("errcode")
    private Integer errcode;
    @JsonProperty("errmsg")
    private String errmsg;
    @JsonProperty("file_list")
    private List<FileList> fileList;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class FileList implements Serializable {
        @JsonProperty("fileid")
        private String fileid;
        @JsonProperty("download_url")
        private String downloadUrl;
        @JsonProperty("status")
        private Integer status;
        @JsonProperty("errmsg")
        private String errmsg;
    }
}
