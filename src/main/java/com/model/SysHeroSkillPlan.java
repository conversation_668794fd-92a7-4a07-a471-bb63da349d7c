package com.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 英雄技能加点方案实体类
 */
@Data
@TableName("sys_hero_skill_plan")
public class SysHeroSkillPlan {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    private Integer heroId;
    
    private Integer level;
    
    private String skillImg;
    
    private LocalDateTime createTime;
}