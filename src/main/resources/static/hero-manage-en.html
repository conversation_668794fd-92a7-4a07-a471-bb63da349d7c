<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DOTA Hero Management System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .hero-list {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .hero-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .hero-item:hover {
            background-color: #f8f9fa;
        }
        
        .hero-item.active {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        
        .hero-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin-right: 15px;
            object-fit: cover;
        }
        
        .hero-info h3 {
            margin-bottom: 5px;
            color: #333;
        }
        
        .hero-info p {
            color: #666;
            font-size: 14px;
        }
        
        .hero-detail {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: none;
        }
        
        .detail-section {
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .section-header {
            background-color: #f5f5f5;
            padding: 15px;
            font-weight: bold;
            color: #333;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .section-content {
            padding: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        
        .skill-plan-container,
        .equipment-container,
        .relation-container {
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 15px;
            background-color: #fafafa;
        }
        
        .skill-item,
        .equipment-item,
        .relation-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border: 1px solid #e0e0e0;
        }
        
        .skill-item input,
        .equipment-item input,
        .relation-item input {
            flex: 1;
            margin-right: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #0056b3;
        }
        
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background-color: #1e7e34;
        }
        
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #c82333;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #545b62;
        }
        
        .equipment-type {
            font-weight: bold;
            color: #495057;
            margin-top: 20px;
            margin-bottom: 10px;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 4px;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        
        .success {
            color: #155724;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        
        .two-column {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
        }
        
        @media (max-width: 768px) {
            .two-column {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 DOTA Hero Management System</h1>
            <p>View and edit detailed hero information</p>
        </div>
        
        <div class="two-column">
            <!-- Hero List -->
            <div class="hero-list">
                <h2>Hero List</h2>
                <div id="heroListContainer">
                    <div class="loading">Loading...</div>
                </div>
            </div>
            
            <!-- Hero Detail Editor -->
            <div class="hero-detail" id="heroDetail">
                <div id="messageContainer"></div>
                
                <!-- Basic Information -->
                <div class="detail-section">
                    <div class="section-header">Basic Information</div>
                    <div class="section-content">
                        <div class="form-group">
                            <label>Hero Name</label>
                            <input type="text" id="heroName" placeholder="Enter hero name">
                        </div>
                        <div class="form-group">
                            <label>Hero Nickname</label>
                            <input type="text" id="heroNickName" placeholder="Enter hero nickname">
                        </div>
                        <div class="form-group">
                            <label>Avatar URL</label>
                            <input type="text" id="heroImg" placeholder="Enter avatar URL">
                        </div>
                        <div class="form-group">
                            <label>Attribute Type</label>
                            <select id="heroAttribute">
                                <option value="0">Intelligence</option>
                                <option value="1">Agility</option>
                                <option value="2">Strength</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Faction</label>
                            <select id="heroCamp">
                                <option value="0">Neutral</option>
                                <option value="1">Radiant</option>
                                <option value="2">Dire</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- Skill Information -->
                <div class="detail-section">
                    <div class="section-header">Skill Information</div>
                    <div class="section-content">
                        <div class="form-group">
                            <label>Skill Description</label>
                            <textarea id="skillInfo" placeholder="Enter skill description"></textarea>
                        </div>
                        <div class="form-group">
                            <label>Skill Build</label>
                            <div class="skill-plan-container" id="skillPlanContainer">
                                <!-- Dynamic skill items -->
                            </div>
                            <button type="button" class="btn btn-secondary" onclick="addSkillItem()">Add Skill</button>
                        </div>
                    </div>
                </div>
                
                <!-- Equipment Information -->
                <div class="detail-section">
                    <div class="section-header">Equipment Information</div>
                    <div class="section-content">
                        <div class="form-group">
                            <label>Equipment Description</label>
                            <textarea id="equipmentInfo" placeholder="Enter equipment description"></textarea>
                        </div>
                        <div class="form-group">
                            <label>Equipment Build</label>
                            <div class="equipment-container" id="equipmentContainer">
                                <!-- Dynamic equipment items -->
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Hero Relations -->
                <div class="detail-section">
                    <div class="section-header">Hero Relations</div>
                    <div class="section-content">
                        <div class="form-group">
                            <label>Synergy Heroes</label>
                            <div class="relation-container" id="cooperateContainer">
                                <!-- Dynamic synergy heroes -->
                            </div>
                            <button type="button" class="btn btn-secondary" onclick="addCooperateItem()">Add Synergy Hero</button>
                        </div>
                        <div class="form-group">
                            <label>Counter Heroes</label>
                            <div class="relation-container" id="restraintContainer">
                                <!-- Dynamic counter heroes -->
                            </div>
                            <button type="button" class="btn btn-secondary" onclick="addRestraintItem()">Add Counter Hero</button>
                        </div>
                    </div>
                </div>
                
                <!-- Detailed Description -->
                <div class="detail-section">
                    <div class="section-header">Detailed Description</div>
                    <div class="section-content">
                        <div class="form-group">
                            <label>Hero Description</label>
                            <textarea id="heroInfo" placeholder="Enter detailed hero description"></textarea>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div style="text-align: center; margin-top: 30px;">
                    <button type="button" class="btn btn-success" onclick="saveHero()" style="margin-right: 10px;">Save Changes</button>
                    <button type="button" class="btn btn-secondary" onclick="resetForm()">Reset</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentHeroId = null;
        let currentHeroData = null;

        // Initialize after page load
        document.addEventListener('DOMContentLoaded', function() {
            loadHeroesList();
        });

        // Load heroes list
        async function loadHeroesList() {
            try {
                console.log('Loading heroes list...');
                const response = await fetch('/manage/dota/api/heroes');
                console.log('API response status:', response.status);
                
                const result = await response.json();
                console.log('API response data:', result);
                
                if (result.code === 200) {
                    if (result.data && result.data.length > 0) {
                        renderHeroesList(result.data);
                    } else {
                        document.getElementById('heroListContainer').innerHTML = 
                            '<div class="loading">No hero data available, please sync data first</div>';
                    }
                } else {
                    showError('Failed to load heroes list: ' + (result.message || 'Unknown error'));
                }
            } catch (error) {
                console.error('Error loading heroes list:', error);
                showError('Failed to load heroes list: ' + error.message);
                document.getElementById('heroListContainer').innerHTML = 
                    '<div class="error">Cannot connect to server, please check network connection</div>';
            }
        }

        // Render heroes list
        function renderHeroesList(heroes) {
            const container = document.getElementById('heroListContainer');
            
            if (!heroes || heroes.length === 0) {
                container.innerHTML = '<div class="loading">No hero data available</div>';
                return;
            }
            
            const html = heroes.map(hero => `
                <div class="hero-item" onclick="selectHero(${hero.id})">
                    <img src="${hero.img || '/default-avatar.png'}" alt="${hero.name || 'Unknown Hero'}" class="hero-avatar" onerror="this.src='/default-avatar.png'">
                    <div class="hero-info">
                        <h3>${hero.name || 'Unknown Hero'}</h3>
                        <p>${hero.nickName || '??'} | ${getAttributeName(hero.attribute)} | ${getCampName(hero.camp)}</p>
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }

        // Select hero
        async function selectHero(heroId) {
            // Update selection state
            document.querySelectorAll('.hero-item').forEach(item => {
                item.classList.remove('active');
            });
            event.currentTarget.classList.add('active');
            
            currentHeroId = heroId;
            
            try {
                const response = await fetch(`/manage/dota/api/hero/${heroId}`);
                const result = await response.json();
                
                if (result.code === 200) {
                    currentHeroData = result.data;
                    renderHeroDetail(result.data);
                    document.getElementById('heroDetail').style.display = 'block';
                } else {
                    showError('Failed to load hero details: ' + result.message);
                }
            } catch (error) {
                showError('Failed to load hero details: ' + error.message);
            }
        }

        // Render hero details
        function renderHeroDetail(hero) {
            // Basic information
            document.getElementById('heroName').value = hero.name || '';
            document.getElementById('heroNickName').value = hero.nickName || '';
            document.getElementById('heroImg').value = hero.img || '';
            document.getElementById('heroAttribute').value = hero.attribute || 0;
            document.getElementById('heroCamp').value = hero.camp || 0;
            document.getElementById('skillInfo').value = hero.skillInfo || '';
            document.getElementById('equipmentInfo').value = hero.equipmentInfo || '';
            document.getElementById('heroInfo').value = hero.info || '';
            
            // Skill build
            renderSkillPlan(hero.skillPlan || []);
            
            // Equipment build
            renderEquipmentPlan(hero.equipmentPlan || {});
            
            // Synergy heroes
            renderCooperateHeroes(hero.cooperate || []);
            
            // Counter heroes
            renderRestraintHeroes(hero.restraint || []);
        }

        // Render skill build
        function renderSkillPlan(skillPlan) {
            const container = document.getElementById('skillPlanContainer');
            const html = skillPlan.map((skill, index) => `
                <div class="skill-item">
                    <span style="width: 60px; text-align: center;">Lv${index + 1}</span>
                    <input type="text" value="${skill}" placeholder="Skill image URL" onchange="updateSkillPlan()">
                    <button type="button" class="btn btn-danger" onclick="removeSkillItem(this)">Remove</button>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }

        // Render equipment build
        function renderEquipmentPlan(equipmentPlan) {
            const container = document.getElementById('equipmentContainer');
            let html = '';
            
            const equipmentTypes = ['Starting Items', 'Core Items', 'Late Game Items'];
            const equipmentTypeMap = {
                'Starting Items': 'Starting Items',
                'Core Items': 'Core Items', 
                'Late Game Items': 'Late Game Items'
            };
            
            // Map Chinese to English for display
            const chineseToEnglish = {
                '出门装': 'Starting Items',
                '中期核心装': 'Core Items',
                '后期神装': 'Late Game Items'
            };
            
            equipmentTypes.forEach(type => {
                html += `<div class="equipment-type">${type}</div>`;
                
                // Try both English and Chinese keys
                let items = equipmentPlan[type] || [];
                if (items.length === 0) {
                    // Try Chinese key
                    const chineseKey = Object.keys(chineseToEnglish).find(key => chineseToEnglish[key] === type);
                    if (chineseKey) {
                        items = equipmentPlan[chineseKey] || [];
                    }
                }
                
                items.forEach((item, index) => {
                    html += `
                        <div class="equipment-item" data-type="${type}">
                            <input type="text" value="${item.img}" placeholder="Equipment image URL" onchange="updateEquipmentPlan()">
                            <input type="text" value="${item.description || ''}" placeholder="Equipment description" onchange="updateEquipmentPlan()">
                            <button type="button" class="btn btn-danger" onclick="removeEquipmentItem(this)">Remove</button>
                        </div>
                    `;
                });
                
                html += `<button type="button" class="btn btn-secondary" onclick="addEquipmentItem('${type}')">Add ${type}</button>`;
            });
            
            container.innerHTML = html;
        }

        // Render synergy heroes
        function renderCooperateHeroes(cooperate) {
            const container = document.getElementById('cooperateContainer');
            const html = cooperate.map(hero => `
                <div class="relation-item">
                    <input type="text" value="${hero.img}" placeholder="Hero image URL" onchange="updateCooperate()">
                    <input type="text" value="${hero.name}" placeholder="Hero name" onchange="updateCooperate()">
                    <button type="button" class="btn btn-danger" onclick="removeRelationItem(this)">Remove</button>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }

        // Render counter heroes
        function renderRestraintHeroes(restraint) {
            const container = document.getElementById('restraintContainer');
            const html = restraint.map(hero => `
                <div class="relation-item">
                    <input type="text" value="${hero.img}" placeholder="Hero image URL" onchange="updateRestraint()">
                    <input type="text" value="${hero.name}" placeholder="Hero name" onchange="updateRestraint()">
                    <button type="button" class="btn btn-danger" onclick="removeRelationItem(this)">Remove</button>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }

        // Add skill item
        function addSkillItem() {
            const container = document.getElementById('skillPlanContainer');
            const level = container.children.length + 1;
            const html = `
                <div class="skill-item">
                    <span style="width: 60px; text-align: center;">Lv${level}</span>
                    <input type="text" value="" placeholder="Skill image URL" onchange="updateSkillPlan()">
                    <button type="button" class="btn btn-danger" onclick="removeSkillItem(this)">Remove</button>
                </div>
            `;
            container.insertAdjacentHTML('beforeend', html);
        }

        // Remove skill item
        function removeSkillItem(button) {
            button.parentElement.remove();
            updateSkillLevels();
        }

        // Update skill levels
        function updateSkillLevels() {
            const skillItems = document.querySelectorAll('#skillPlanContainer .skill-item');
            skillItems.forEach((item, index) => {
                const span = item.querySelector('span');
                span.textContent = `Lv${index + 1}`;
            });
        }

        // Add equipment item
        function addEquipmentItem(type) {
            const container = document.getElementById('equipmentContainer');
            const html = `
                <div class="equipment-item" data-type="${type}">
                    <input type="text" value="" placeholder="Equipment image URL" onchange="updateEquipmentPlan()">
                    <input type="text" value="" placeholder="Equipment description" onchange="updateEquipmentPlan()">
                    <button type="button" class="btn btn-danger" onclick="removeEquipmentItem(this)">Remove</button>
                </div>
            `;
            
            // Find the corresponding type button and insert before it
            const buttons = container.querySelectorAll('button');
            for (let button of buttons) {
                if (button.textContent.includes(type)) {
                    button.insertAdjacentHTML('beforebegin', html);
                    break;
                }
            }
        }

        // Remove equipment item
        function removeEquipmentItem(button) {
            button.parentElement.remove();
        }

        // Add synergy item
        function addCooperateItem() {
            const container = document.getElementById('cooperateContainer');
            const html = `
                <div class="relation-item">
                    <input type="text" value="" placeholder="Hero image URL" onchange="updateCooperate()">
                    <input type="text" value="" placeholder="Hero name" onchange="updateCooperate()">
                    <button type="button" class="btn btn-danger" onclick="removeRelationItem(this)">Remove</button>
                </div>
            `;
            container.insertAdjacentHTML('beforeend', html);
        }

        // Add counter item
        function addRestraintItem() {
            const container = document.getElementById('restraintContainer');
            const html = `
                <div class="relation-item">
                    <input type="text" value="" placeholder="Hero image URL" onchange="updateRestraint()">
                    <input type="text" value="" placeholder="Hero name" onchange="updateRestraint()">
                    <button type="button" class="btn btn-danger" onclick="removeRelationItem(this)">Remove</button>
                </div>
            `;
            container.insertAdjacentHTML('beforeend', html);
        }

        // Remove relation item
        function removeRelationItem(button) {
            button.parentElement.remove();
        }

        // Save hero
        async function saveHero() {
            if (!currentHeroId) {
                showError('Please select a hero first');
                return;
            }

            const updateData = {
                name: document.getElementById('heroName').value,
                nickName: document.getElementById('heroNickName').value,
                img: document.getElementById('heroImg').value,
                attribute: parseInt(document.getElementById('heroAttribute').value),
                camp: parseInt(document.getElementById('heroCamp').value),
                skillInfo: document.getElementById('skillInfo').value,
                equipmentInfo: document.getElementById('equipmentInfo').value,
                info: document.getElementById('heroInfo').value,
                skillPlan: getSkillPlanData(),
                equipmentPlan: getEquipmentPlanData(),
                cooperate: getCooperateData(),
                restraint: getRestraintData()
            };

            try {
                const response = await fetch(`/manage/dota/api/hero/${currentHeroId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(updateData)
                });

                const result = await response.json();
                
                if (result.code === 200) {
                    showSuccess('Hero information saved successfully!');
                    // Reload heroes list
                    loadHeroesList();
                } else {
                    showError('Save failed: ' + result.message);
                }
            } catch (error) {
                showError('Save failed: ' + error.message);
            }
        }

        // Get skill plan data
        function getSkillPlanData() {
            const skillItems = document.querySelectorAll('#skillPlanContainer .skill-item input');
            return Array.from(skillItems).map(input => input.value).filter(value => value.trim());
        }

        // Get equipment plan data
        function getEquipmentPlanData() {
            const equipmentPlan = {};
            const equipmentItems = document.querySelectorAll('#equipmentContainer .equipment-item');
            
            equipmentItems.forEach(item => {
                const type = item.getAttribute('data-type');
                const inputs = item.querySelectorAll('input');
                const img = inputs[0].value;
                const description = inputs[1].value;
                
                if (img.trim()) {
                    if (!equipmentPlan[type]) {
                        equipmentPlan[type] = [];
                    }
                    equipmentPlan[type].push({
                        img: img,
                        description: description,
                        sortOrder: equipmentPlan[type].length + 1
                    });
                }
            });
            
            return equipmentPlan;
        }

        // Get synergy data
        function getCooperateData() {
            const cooperateItems = document.querySelectorAll('#cooperateContainer .relation-item');
            return Array.from(cooperateItems).map(item => {
                const inputs = item.querySelectorAll('input');
                return {
                    img: inputs[0].value,
                    name: inputs[1].value
                };
            }).filter(item => item.img.trim() || item.name.trim());
        }

        // Get counter data
        function getRestraintData() {
            const restraintItems = document.querySelectorAll('#restraintContainer .relation-item');
            return Array.from(restraintItems).map(item => {
                const inputs = item.querySelectorAll('input');
                return {
                    img: inputs[0].value,
                    name: inputs[1].value
                };
            }).filter(item => item.img.trim() || item.name.trim());
        }

        // Reset form
        function resetForm() {
            if (currentHeroData) {
                renderHeroDetail(currentHeroData);
                showSuccess('Form has been reset');
            }
        }

        // Show error message
        function showError(message) {
            const container = document.getElementById('messageContainer');
            container.innerHTML = `<div class="error">${message}</div>`;
            setTimeout(() => {
                container.innerHTML = '';
            }, 5000);
        }

        // Show success message
        function showSuccess(message) {
            const container = document.getElementById('messageContainer');
            container.innerHTML = `<div class="success">${message}</div>`;
            setTimeout(() => {
                container.innerHTML = '';
            }, 3000);
        }

        // Get attribute name
        function getAttributeName(attribute) {
            const names = ['Intelligence', 'Agility', 'Strength'];
            return names[attribute] || 'Unknown';
        }

        // Get camp name
        function getCampName(camp) {
            const names = ['Neutral', 'Radiant', 'Dire'];
            return names[camp] || 'Unknown';
        }

        // Update functions (placeholders)
        function updateSkillPlan() {}
        function updateEquipmentPlan() {}
        function updateCooperate() {}
        function updateRestraint() {}
    </script>
</body>
</html>