package com.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Title: BatchDownloadFile
 * <AUTHOR>
 * @Package com.model
 * @Date 2025/7/17 12:26
 * @description:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class BatchDownloadFile implements Serializable {

    @JsonProperty("env")
    private String env;
    @JsonProperty("file_list")
    private List<FileList> fileList;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class FileList implements Serializable {

        @JsonProperty("fileid")
        private String fileid;
        @JsonProperty("max_age")
        private Integer maxAge;
    }
}
