package com.miniapp.service;

import com.model.dto.HeroDetailDTO;
import com.model.dto.HeroUpdateDTO;

import java.util.List;

/**
 * 英雄管理服务接口
 */
public interface HeroManageService {
    
    /**
     * 获取所有英雄的详细信息
     * @return 英雄详情列表
     */
    List<HeroDetailDTO> getAllHeroesWithDetails();
    
    /**
     * 更新英雄详细信息
     * @param updateDTO 更新数据
     * @return 是否成功
     */
    boolean updateHeroDetails(HeroUpdateDTO updateDTO);
}