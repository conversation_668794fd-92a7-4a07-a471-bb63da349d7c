# Product Overview

This is a **Dota Mini App** - a WeChat mini-program backend service that provides Dota 2 hero information and game data.

## Core Features
- Hero listing with filtering by attribute and camp
- Detailed hero information including skills, equipment, and relationships
- Hero data management with Redis caching
- WeChat mini-program integration with cloud storage
- Database-driven hero data with relational structure

## Key Entities
- **Heroes**: Core game characters with attributes, camps, and detailed information
- **Skills**: Hero abilities with upgrade plans and descriptions
- **Equipment**: Item builds and recommendations
- **Relationships**: Hero cooperation and restraint data

## Target Platform
WeChat Mini Program ecosystem with cloud storage integration for images and data persistence.