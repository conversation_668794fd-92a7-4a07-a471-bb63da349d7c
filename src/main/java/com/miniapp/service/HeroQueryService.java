package com.miniapp.service;

import com.model.dto.HeroDetailDTO;
import com.model.SysHero;

import java.util.List;

/**
 * 英雄查询服务接口
 */
public interface HeroQueryService {

    /**
     * 获取英雄列表（首页）
     * @param attribute 属性类型
     * @param camp 阵营
     * @return 英雄列表
     */
    List<SysHero> getHeroList(String attribute, String camp);

    /**
     * 获取英雄详情（组合拆分后的数据）
     * @param heroId 英雄ID
     * @return 英雄详情DTO
     */
    HeroDetailDTO getHeroDetail(Integer heroId);
}
